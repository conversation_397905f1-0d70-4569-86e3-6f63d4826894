import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';

class FishCategoryCard extends StatelessWidget {
  final AuctionCategory category;
  final VoidCallback? onTap;
  final bool isCompact;

  const FishCategoryCard({
    super.key,
    required this.category,
    this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section
            Expanded(
              flex: 3,
              child: _buildImageSection(context),
            ),
            
            // Content Section
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      category.getLocalizedName(
                        Localizations.localeOf(context).languageCode,
                      ),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (category.description != null && category.description!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        category.getLocalizedDescription(
                          Localizations.localeOf(context).languageCode,
                        ) ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(AppConstants.borderRadius),
        topRight: Radius.circular(AppConstants.borderRadius),
      ),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: _buildImage(),
      ),
    );
  }

  Widget _buildImage() {
    // Use category image if available, otherwise use default fish images
    if (category.icon != null && category.icon!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: category.icon!,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildPlaceholderImage(),
        errorWidget: (context, url, error) => _buildDefaultCategoryImage(),
      );
    } else {
      return _buildDefaultCategoryImage();
    }
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: AppColors.background,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildDefaultCategoryImage() {
    // Use different default images based on category name
    final categoryImage = _getDefaultCategoryImage();
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: categoryImage['gradient'] as List<Color>,
        ),
      ),
      child: Center(
        child: Icon(
          categoryImage['icon'] as IconData,
          size: 48,
          color: Colors.white,
        ),
      ),
    );
  }

  Map<String, dynamic> _getDefaultCategoryImage() {
    final categoryName = category.name.toLowerCase();
    
    if (categoryName.contains('sea') || categoryName.contains('marine') || categoryName.contains('ocean')) {
      return {
        'icon': Icons.waves,
        'gradient': [Colors.blue.shade400, Colors.blue.shade600],
      };
    } else if (categoryName.contains('fresh') || categoryName.contains('river') || categoryName.contains('lake')) {
      return {
        'icon': Icons.water,
        'gradient': [Colors.teal.shade400, Colors.teal.shade600],
      };
    } else if (categoryName.contains('shell') || categoryName.contains('crab') || categoryName.contains('lobster')) {
      return {
        'icon': Icons.set_meal,
        'gradient': [Colors.orange.shade400, Colors.orange.shade600],
      };
    } else if (categoryName.contains('processed') || categoryName.contains('frozen') || categoryName.contains('canned')) {
      return {
        'icon': Icons.inventory_2,
        'gradient': [Colors.purple.shade400, Colors.purple.shade600],
      };
    } else if (categoryName.contains('salmon') || categoryName.contains('trout')) {
      return {
        'icon': Icons.phishing,
        'gradient': [Colors.pink.shade400, Colors.pink.shade600],
      };
    } else if (categoryName.contains('tuna') || categoryName.contains('mackerel')) {
      return {
        'icon': Icons.sailing,
        'gradient': [Colors.indigo.shade400, Colors.indigo.shade600],
      };
    } else {
      return {
        'icon': Icons.phishing,
        'gradient': [AppColors.primary, AppColors.primaryDark],
      };
    }
  }
}
