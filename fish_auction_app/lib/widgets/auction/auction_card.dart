import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auth_provider.dart';

class AuctionCard extends StatelessWidget {
  final Auction auction;
  final VoidCallback? onTap;
  final VoidCallback? onWatchToggle;
  final bool isCompact;
  final bool showWatchButton;

  const AuctionCard({
    super.key,
    required this.auction,
    this.onTap,
    this.onWatchToggle,
    this.isCompact = false,
    this.showWatchButton = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isCompact) {
      return _buildCompactCard(context);
    } else {
      return _buildFullCard(context);
    }
  }

  Widget _buildFullCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppConstants.borderRadius)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section
            _buildImageSection(context),

            // Content Section
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Status
                  _buildTitleAndStatus(context),

                  const SizedBox(height: AppConstants.smallPadding),

                  // Fish Details
                  _buildFishDetails(context),

                  const SizedBox(height: AppConstants.smallPadding),

                  // Price and Bids
                  _buildPriceAndBids(context),

                  const SizedBox(height: AppConstants.smallPadding),

                  // Time Remaining
                  _buildTimeRemaining(context),
                ],
              ),
            ),

            // Wallet Balance Warning (only for live auctions) - Outside padding to prevent overflow
            if (auction.isLive) _buildWalletBalanceWarning(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactCard(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppConstants.borderRadius)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              // Compact Image
              ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                child: SizedBox(width: 60, height: 60, child: _buildImage()),
              ),

              const SizedBox(width: AppConstants.defaultPadding),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and Status
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            auction.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStatusBadge(context),
                      ],
                    ),

                    const SizedBox(height: 4),

                    // Fish Type and Quantity
                    Text(
                      '${auction.fishType} • ${auction.formattedQuantity}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                    ),

                    const SizedBox(height: 4),

                    // Price and Time
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '\$${auction.currentPrice.toStringAsFixed(2)}',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
                        ),
                        if (auction.isLive && auction.hasTimeRemaining)
                          Text(
                            _formatTimeRemaining(auction.timeRemaining),
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(color: AppColors.warning, fontWeight: FontWeight.w500),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return Stack(
      children: [
        // Main Image
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(AppConstants.borderRadius),
            topRight: Radius.circular(AppConstants.borderRadius),
          ),
          child: SizedBox(height: 180, width: double.infinity, child: _buildImage()),
        ),

        // Status Badge
        Positioned(top: AppConstants.smallPadding, left: AppConstants.smallPadding, child: _buildStatusBadge(context)),

        // Watch Button
        if (showWatchButton)
          Positioned(
            top: AppConstants.smallPadding,
            right: AppConstants.smallPadding,
            child: _buildWatchButton(context),
          ),

        // Quality Grade
        Positioned(
          bottom: AppConstants.smallPadding,
          right: AppConstants.smallPadding,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.textPrimary.withOpacity(0.8),
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
            ),
            child: Text(
              'Grade ${auction.qualityGrade}',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImage() {
    if (auction.primaryImage.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: auction.primaryImage,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: AppColors.background,
          child: const Center(child: CircularProgressIndicator()),
        ),
        errorWidget: (context, url, error) => _buildPlaceholderImage(),
      );
    } else {
      return _buildPlaceholderImage();
    }
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: AppColors.background,
      child: const Center(child: Icon(Icons.image_outlined, size: 48, color: AppColors.textHint)),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    Color statusColor;
    String statusText;

    switch (auction.status) {
      case 'live':
        statusColor = AppColors.liveAuction;
        statusText = 'LIVE';
        break;
      case 'scheduled':
        statusColor = AppColors.scheduledAuction;
        statusText = 'SCHEDULED';
        break;
      case 'ended':
        statusColor = AppColors.endedAuction;
        statusText = 'ENDED';
        break;
      default:
        statusColor = AppColors.textSecondary;
        statusText = auction.status.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.smallPadding, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
      ),
      child: Text(
        statusText,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold, fontSize: 10),
      ),
    );
  }

  Widget _buildWatchButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: AppColors.textLight.withOpacity(0.9), shape: BoxShape.circle),
      child: IconButton(
        onPressed: () {
          if (onWatchToggle != null) {
            onWatchToggle!();
          }
        },
        icon: Icon(
          auction.isWatched ? Icons.bookmark : Icons.bookmark_outline,
          color: auction.isWatched ? AppColors.accent : AppColors.textSecondary,
          size: 20,
        ),
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildTitleAndStatus(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            auction.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildFishDetails(BuildContext context) {
    return Row(
      children: [
        Icon(Icons.category_outlined, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Text(auction.fishType, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary)),
        const SizedBox(width: AppConstants.defaultPadding),
        Icon(Icons.scale_outlined, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Text(
          auction.formattedQuantity,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildPriceAndBids(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Price',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
            Text(
              '\$${auction.currentPrice.toStringAsFixed(2)}',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text('Total Bids', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary)),
            Text(
              auction.totalBids.toString(),
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: AppColors.textPrimary, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeRemaining(BuildContext context) {
    if (!auction.isLive) return const SizedBox.shrink();

    final timeRemaining = auction.timeRemaining;
    // Don't show time remaining if auction has expired
    if (!auction.hasTimeRemaining) return const SizedBox.shrink();

    final isEndingSoon = timeRemaining.inHours < 1;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding, vertical: AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: isEndingSoon ? AppColors.error.withOpacity(0.1) : AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(color: isEndingSoon ? AppColors.error : AppColors.warning, width: 1),
      ),
      child: Row(
        children: [
          Icon(Icons.schedule, size: 16, color: isEndingSoon ? AppColors.error : AppColors.warning),
          const SizedBox(width: 4),
          Text(
            'Ends in ${_formatTimeRemaining(timeRemaining)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isEndingSoon ? AppColors.error : AppColors.warning,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimeRemaining(Duration duration) {
    // Handle expired auctions
    if (duration.inSeconds <= 0) {
      return 'Expired';
    }

    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  Widget _buildWalletBalanceWarning(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userWalletBalance = authProvider.user?.walletBalance ?? 0.0;

    // Only show warning if user can't participate
    if (auction.canParticipateWithBalance(userWalletBalance)) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(
        AppConstants.defaultPadding,
        0,
        AppConstants.defaultPadding,
        AppConstants.defaultPadding,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppConstants.borderRadius),
          bottomRight: Radius.circular(AppConstants.borderRadius),
        ),
        border: Border.all(color: Colors.amber.shade300, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.account_balance_wallet_outlined, size: 16, color: Colors.amber.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Need \$${auction.requiredWalletBalance.toStringAsFixed(2)} to bid',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.amber.shade800, fontSize: 12, fontWeight: FontWeight.w600),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
