import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:async';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../models/bid_model.dart';
import '../../providers/auction_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/websocket_service.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/auction/live_bidding_history.dart';
import '../../widgets/auction/auto_bid_manager.dart';
import '../../widgets/auction/auction_status_manager.dart';
import '../location/seller_location_screen.dart';
import '../broker/service_request_screen.dart';
import '../broker/my_service_requests_screen.dart';
import '../payment/earned_auction_screen.dart';
import '../../providers/broker_provider.dart';
import '../../models/broker_service_model.dart';
import '../../services/payment_service.dart';

class AuctionDetailScreen extends StatefulWidget {
  final Auction auction;

  const AuctionDetailScreen({super.key, required this.auction});

  @override
  State<AuctionDetailScreen> createState() => _AuctionDetailScreenState();
}

class _AuctionDetailScreenState extends State<AuctionDetailScreen> with TickerProviderStateMixin {
  TabController? _tabController;
  final PageController _imageController = PageController();
  int _currentImageIndex = 0;
  bool _isLoading = false;

  // WebSocket related
  final WebSocketService _webSocketService = WebSocketService();
  StreamSubscription<Map<String, dynamic>>? _webSocketSubscription;
  StreamSubscription<bool>? _connectionStatusSubscription;
  bool _isWebSocketConnected = false;
  late Auction _currentAuction;

  // Timer related
  Timer? _countdownTimer;
  Duration _timeRemaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    try {
      _currentAuction = widget.auction;
      _timeRemaining = _currentAuction.timeRemaining;
      _loadAuctionDetails();
      _initializeWebSocket();
      _startCountdownTimer();
      _loadBrokerRequests();
    } catch (e) {
      print('Error initializing auction detail screen: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh broker requests when screen comes back into focus
    _loadBrokerRequests();
  }

  void _loadBrokerRequests() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userType = authProvider.userType;

    // Only load broker requests for buyers
    if (userType == 'buyer') {
      final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
      brokerProvider.loadMyServiceRequests();
    }
  }

  void _initializeTabController() {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final isOwner = authProvider.user?.id == widget.auction.seller.id;
      final isDirectBuy = widget.auction.auctionType == 'buy_now';

      // Determine tab count based on auction type
      int tabCount;
      if (isDirectBuy) {
        tabCount = 3; // Direct buy: Details, Broker Services, Seller Info
      } else {
        tabCount = isOwner ? 3 : 4; // Regular auctions: Sellers get 3 tabs, buyers get 4
      }

      _tabController?.dispose();
      _tabController = TabController(length: tabCount, vsync: this);
    } catch (e) {
      print('Error initializing tab controller: $e');
      // Fallback to default tab controller
      _tabController?.dispose();
      _tabController = TabController(length: 4, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _imageController.dispose();
    _webSocketSubscription?.cancel();
    _connectionStatusSubscription?.cancel();
    _webSocketService.disconnect();
    _countdownTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadAuctionDetails() async {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    await auctionProvider.loadAuctionDetails(widget.auction.id);
    // Load user bids to check if user has bid on this auction
    await auctionProvider.loadUserBids();
  }

  /// Initialize WebSocket connection for real-time updates
  Future<void> _initializeWebSocket() async {
    try {
      // Connect to auction WebSocket
      await _webSocketService.connectToAuction(_currentAuction.id.toString());

      // Listen to WebSocket messages
      _webSocketSubscription = _webSocketService.messageStream.listen(
        _handleWebSocketMessage,
        onError: (error) {
          print('❌ WebSocket stream error: $error');
          // Don't crash the app on WebSocket errors
          if (mounted) {
            setState(() {
              _isWebSocketConnected = false;
            });
          }
        },
      );

      // Listen to connection status
      _connectionStatusSubscription = _webSocketService.connectionStatusStream.listen(
        (isConnected) {
          if (mounted) {
            setState(() {
              _isWebSocketConnected = isConnected;
            });
            print('🔌 WebSocket connection status: ${isConnected ? "Connected" : "Disconnected"}');
          }
        },
        onError: (error) {
          print('❌ WebSocket connection status error: $error');
          if (mounted) {
            setState(() {
              _isWebSocketConnected = false;
            });
          }
        },
      );

      print('✅ WebSocket initialized for auction ${_currentAuction.id}');
    } catch (e) {
      print('❌ Failed to initialize WebSocket: $e');
      // Ensure the app doesn't crash if WebSocket fails
      if (mounted) {
        setState(() {
          _isWebSocketConnected = false;
        });
      }
    }
  }

  /// Handle incoming WebSocket messages
  void _handleWebSocketMessage(Map<String, dynamic> message) {
    final messageType = message['type'];
    final data = message['data'];

    print('📨 Received WebSocket message: $messageType');

    switch (messageType) {
      case 'bid_update':
        _handleBidUpdate(data);
        break;
      case 'auction_status':
        _handleAuctionStatusUpdate(data);
        break;
      case 'user_joined':
        _handleUserJoined(data);
        break;
      case 'auction_ended':
        _handleAuctionEnded(data);
        break;
      case 'connection_closed':
        _handleConnectionClosed();
        break;
      default:
        print('🔍 Unknown WebSocket message type: $messageType');
    }
  }

  /// Handle bid update from WebSocket
  void _handleBidUpdate(Map<String, dynamic>? data) {
    if (data == null) return;

    setState(() {
      // Update current auction with new bid data
      _currentAuction = _currentAuction.copyWith(
        currentPrice: double.tryParse(data['current_price']?.toString() ?? '0') ?? _currentAuction.currentPrice,
        totalBids: data['total_bids'] ?? _currentAuction.totalBids,
      );
    });

    // Update timer in case auction status changed
    _updateTimer();

    // Don't reload bids from API - WebSocket service handles bid updates
    // The LiveBiddingHistory widget will automatically update via WebSocket stream

    // Show notification for bid updates
    final bidType = data['bid_type']?.toString() ?? 'manual';
    final isAutoBid = bidType == 'auto';
    final amount = data['amount']?.toString() ?? '0';
    final bidder = data['bidder']?.toString() ?? 'Unknown';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${isAutoBid ? '🤖 Auto-bid' : '💰 New bid'}: \$$amount by $bidder',
          style: const TextStyle(color: Colors.white),
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: isAutoBid ? Colors.orange : AppColors.success,
      ),
    );
  }

  /// Handle auction status update
  void _handleAuctionStatusUpdate(Map<String, dynamic>? data) {
    if (data == null) return;

    setState(() {
      _currentAuction = _currentAuction.copyWith(
        currentPrice: double.tryParse(data['current_price']?.toString() ?? '0') ?? _currentAuction.currentPrice,
        targetPrice: data['target_price'] != null
            ? double.tryParse(data['target_price']?.toString() ?? '0')
            : _currentAuction.targetPrice,
        totalBids: data['total_bids'] ?? _currentAuction.totalBids,
        status: data['status'] ?? _currentAuction.status,
      );
    });
  }

  /// Handle user joined notification
  void _handleUserJoined(Map<String, dynamic>? data) {
    if (data == null) return;

    // Optionally show user joined notification
    print('👤 User joined: ${data['username']} (${data['user_type']})');
  }

  /// Handle auction ended notification
  void _handleAuctionEnded(Map<String, dynamic>? data) {
    if (data == null) return;

    setState(() {
      _currentAuction = _currentAuction.copyWith(status: 'ended');
    });

    // Show auction ended dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Auction Ended'),
        content: const Text('This auction has ended. Thank you for participating!'),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
      ),
    );
  }

  /// Handle WebSocket connection closed
  void _handleConnectionClosed() {
    print('🔌 WebSocket connection closed, attempting to reconnect...');

    // Attempt to reconnect after a delay
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _webSocketService.reconnect();
      }
    });
  }

  /// Start countdown timer for real-time updates
  void _startCountdownTimer() {
    _countdownTimer?.cancel();

    if (_currentAuction.isLive && _currentAuction.hasTimeRemaining) {
      _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _timeRemaining = _currentAuction.timeRemaining;

            // Stop timer if auction ended
            if (_timeRemaining.inSeconds <= 0) {
              timer.cancel();
              _currentAuction = _currentAuction.copyWith(status: 'ended');
            }
          });
        } else {
          timer.cancel();
        }
      });
    }
  }

  /// Stop countdown timer
  void _stopCountdownTimer() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
  }

  /// Update timer when auction data changes
  void _updateTimer() {
    if (_currentAuction.isLive && _currentAuction.hasTimeRemaining) {
      _timeRemaining = _currentAuction.timeRemaining;
      if (_countdownTimer == null || !_countdownTimer!.isActive) {
        _startCountdownTimer();
      }
    } else {
      _stopCountdownTimer();
    }
  }

  @override
  Widget build(BuildContext context) {
    try {
      final localizations = AppLocalizations.of(context);

      return Scaffold(
        body: Consumer<AuctionProvider>(
          builder: (context, auctionProvider, child) {
            try {
              // Always use the current auction state (updated by WebSocket)
              // Don't override with provider data as it might be stale
              final auction = _currentAuction;

              return LoadingOverlay(
                isLoading: auctionProvider.isLoading,
                child: CustomScrollView(
                  slivers: [
                    // App Bar with Images
                    _buildSliverAppBar(auction, localizations, auctionProvider),

                    // Content
                    SliverToBoxAdapter(
                      child: Column(
                        children: [
                          // Auction Info
                          _buildAuctionInfo(auction, localizations),

                          // Auction Status Manager (for sellers only)
                          _buildAuctionStatusManager(auction),

                          // Action Buttons
                          _buildActionButtons(auction, localizations),

                          // Tabs
                          _buildTabSection(auction, localizations),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            } catch (e) {
              print('Error in auction detail builder: $e');
              return const Center(child: Text('Error loading auction details'));
            }
          },
        ),
      );
    } catch (e) {
      print('Error in auction detail build: $e');
      return Scaffold(
        appBar: AppBar(title: const Text('Auction Details')),
        body: const Center(child: Text('Error loading auction')),
      );
    }
  }

  Widget _buildSliverAppBar(Auction auction, AppLocalizations localizations, AuctionProvider auctionProvider) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(background: _buildImageCarousel(auction)),
      actions: [
        // WebSocket connection status indicator
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: Icon(
            _isWebSocketConnected ? Icons.wifi : Icons.wifi_off,
            color: _isWebSocketConnected ? Colors.green : Colors.red,
            size: 20,
          ),
        ),
        // Location icon - always visible but access controlled
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: GestureDetector(
            onTap: () => _openFullMapScreen(auction),
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(20)),
              child: const Icon(Icons.location_on, color: Colors.white, size: 18),
            ),
          ),
        ),
        IconButton(
          onPressed: () => _toggleWatchlist(auction),
          icon: Icon(
            auction.isWatched ? Icons.bookmark : Icons.bookmark_outline,
            color: auction.isWatched ? AppColors.accent : AppColors.textLight,
          ),
        ),
        IconButton(
          onPressed: () => _shareAuction(auction),
          icon: const Icon(Icons.share, color: AppColors.textLight),
        ),
      ],
    );
  }

  Widget _buildImageCarousel(Auction auction) {
    // Use images if available, otherwise use a placeholder
    List<String> images = [];
    if (auction.images.isNotEmpty) {
      images = auction.images;
    } else {
      // Use placeholder image if no images available
      images = ['https://via.placeholder.com/400x300?text=Fish+Image'];
    }

    return Stack(
      children: [
        PageView.builder(
          controller: _imageController,
          onPageChanged: (index) {
            setState(() {
              _currentImageIndex = index;
            });
          },
          itemCount: images.length,
          itemBuilder: (context, index) {
            final imageUrl = images[index];
            return CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: AppColors.background,
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => Container(
                color: AppColors.background,
                child: const Center(child: Icon(Icons.image_outlined, size: 64, color: AppColors.textHint)),
              ),
            );
          },
        ),

        // Image Indicators
        if (images.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: images.asMap().entries.map((entry) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentImageIndex == entry.key ? AppColors.textLight : AppColors.textLight.withOpacity(0.4),
                  ),
                );
              }).toList(),
            ),
          ),

        // Status Badge
        Positioned(top: 60, left: 16, child: _buildStatusBadge(auction)),
      ],
    );
  }

  Widget _buildStatusBadge(Auction auction) {
    Color statusColor;
    String statusText;

    switch (auction.status) {
      case 'live':
        statusColor = AppColors.liveAuction;
        statusText = 'LIVE';
        break;
      case 'scheduled':
        statusColor = AppColors.scheduledAuction;
        statusText = 'SCHEDULED';
        break;
      case 'ended':
        statusColor = AppColors.endedAuction;
        statusText = 'ENDED';
        break;
      default:
        statusColor = AppColors.textSecondary;
        statusText = auction.status.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(color: statusColor, borderRadius: BorderRadius.circular(16)),
      child: Text(
        statusText,
        style: const TextStyle(color: AppColors.textLight, fontWeight: FontWeight.bold, fontSize: 12),
      ),
    );
  }

  Widget _buildAuctionInfo(Auction auction, AppLocalizations localizations) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(auction.title, style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold)),

          const SizedBox(height: AppConstants.smallPadding),

          // Fish Details
          Row(
            children: [
              Icon(Icons.category_outlined, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                auction.fishType,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              ),
              const SizedBox(width: 16),
              Icon(Icons.scale_outlined, size: 16, color: AppColors.textSecondary),
              const SizedBox(width: 4),
              Text(
                auction.formattedQuantity,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Grade ${auction.qualityGrade}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: AppColors.primary, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Price and Bids
          Row(
            children: [
              Expanded(
                child: _buildInfoCard(
                  title: 'Current Price',
                  value: '\$${auction.currentPrice.toStringAsFixed(2)}',
                  color: AppColors.primary,
                  icon: Icons.attach_money,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildInfoCard(
                  title: 'Total Bids',
                  value: auction.totalBids.toString(),
                  color: AppColors.secondary,
                  icon: Icons.gavel,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Time Remaining
          if (auction.isLive && auction.hasTimeRemaining) _buildTimeRemainingCard(auction),
        ],
      ),
    );
  }

  Widget _buildInfoCard({required String title, required String value, required Color color, required IconData icon}) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: color, fontWeight: FontWeight.bold),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRemainingCard(Auction auction) {
    // Use real-time timer value instead of static auction time
    final timeRemaining = _timeRemaining.inSeconds > 0 ? _timeRemaining : auction.timeRemaining;
    final isEndingSoon = timeRemaining.inHours < 1;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: isEndingSoon ? AppColors.error.withOpacity(0.1) : AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: isEndingSoon ? AppColors.error : AppColors.warning),
      ),
      child: Row(
        children: [
          Icon(Icons.schedule, color: isEndingSoon ? AppColors.error : AppColors.warning),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            'Ends in ${_formatTimeRemaining(timeRemaining)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: isEndingSoon ? AppColors.error : AppColors.warning,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuctionStatusManager(Auction auction) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isOwner = authProvider.user?.id == auction.seller.id;

    // Only show status manager for sellers
    if (!isOwner) {
      return const SizedBox.shrink();
    }

    return AuctionStatusManager(
      auction: auction,
      onStatusChanged: () {
        // Refresh auction details when status changes
        _loadAuctionDetails();
        setState(() {
          // Update current auction state
          _currentAuction = auction;
        });
      },
    );
  }

  Widget _buildActionButtons(Auction auction, AppLocalizations localizations) {
    final authProvider = Provider.of<AuthProvider>(context);

    // Check if current user is the seller
    final isOwner = authProvider.user?.id == auction.seller.id;

    // Check if this is a direct buy auction
    final isDirectBuy = auction.auctionType == 'buy_now';

    // For direct buy auctions, show different UI
    if (isDirectBuy) {
      return _buildDirectBuyActions(auction, localizations, isOwner, authProvider);
    }

    // Show bidding buttons for live auctions OR draft auctions (which should be live)
    // Also check if user can bid (is buyer) and is not the seller
    final canShowBidding = (auction.isLive || auction.isDraft) && authProvider.canBid && !isOwner;

    if (!canShowBidding) {
      // Show appropriate message based on why bidding is not available
      String message;
      Color color;
      IconData icon;

      if (isOwner) {
        message = 'Your Auction';
        color = AppColors.info;
        icon = Icons.store;
      } else if (auction.isEnded) {
        message = 'Auction Ended';
        color = AppColors.textSecondary;
        icon = Icons.gavel_outlined;
      } else if (!auction.isLive && !auction.isDraft) {
        message = 'Auction ${auction.status}';
        color = AppColors.textSecondary;
        icon = Icons.info_outline;
      } else {
        message = 'Bidding not available';
        color = AppColors.warning;
        icon = Icons.info_outline;
      }

      return Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                message,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(color: color, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Main bidding buttons
          Row(
            children: [
              Expanded(
                child: PrimaryButton(
                  text: localizations.placeBid,
                  onPressed:
                      auction.canParticipateWithBalance(
                        Provider.of<AuthProvider>(context, listen: false).user?.walletBalance ?? 0.0,
                      )
                      ? () => _showBidDialog(auction, localizations)
                      : null,
                  icon: Icons.gavel,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              OutlinedCustomButton(
                text: localizations.autoBid,
                onPressed:
                    auction.canParticipateWithBalance(
                      Provider.of<AuthProvider>(context, listen: false).user?.walletBalance ?? 0.0,
                    )
                    ? () => _showAutoBidDialog(auction, localizations)
                    : null,
                icon: Icons.auto_mode,
                width: 120,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDirectBuyActions(
    Auction auction,
    AppLocalizations localizations,
    bool isOwner,
    AuthProvider authProvider,
  ) {
    if (isOwner) {
      // Show owner message for direct buy
      return Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.store, color: AppColors.info),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                'Your Direct Buy Item',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: AppColors.info, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      );
    }

    if (!auction.isLive) {
      // Show not available message
      return Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.textSecondary.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, color: AppColors.textSecondary),
              const SizedBox(width: AppConstants.smallPadding),
              Text(
                'Item Not Available',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      );
    }

    // Show Buy Now button for buyers
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Price display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                Text(
                  'Fixed Price',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
                ),
                const SizedBox(height: 4),
                Text(
                  '\$${auction.currentPrice.toStringAsFixed(2)}',
                  style: Theme.of(
                    context,
                  ).textTheme.headlineMedium?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Buy Now button
          PrimaryButton(
            text: 'Buy Now',
            onPressed: () => _showBuyNowDialog(auction, localizations),
            icon: Icons.shopping_cart,
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection(Auction auction, AppLocalizations localizations) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isOwner = authProvider.user?.id == auction.seller.id;

    // Initialize tab controller if needed
    if (_tabController == null) {
      _initializeTabController();
    }

    // Check if user has bid on this auction (for location access)
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    final hasUserBid = auctionProvider.hasUserBidOnAuction(auction.id);

    // Check if this is a direct buy auction
    final isDirectBuy = auction.auctionType == 'buy_now';

    // Different tabs based on auction type and user role
    List<Tab> tabs;
    List<Widget> tabViews;

    if (isDirectBuy) {
      // Direct buy auctions: Details, Broker Services, Seller Info
      tabs = [Tab(text: localizations.auctionDetails), Tab(text: 'Broker Services'), Tab(text: 'Seller Info')];
      tabViews = [
        _buildDetailsTab(auction, localizations),
        _buildBrokerServicesTab(auction, localizations),
        _buildSellerInfoTab(auction, localizations),
      ];
    } else {
      // Regular auctions: Different tabs for sellers vs buyers
      tabs = isOwner
          ? [Tab(text: localizations.auctionDetails), Tab(text: 'Live Bidding'), Tab(text: 'Seller Info')]
          : [
              Tab(text: localizations.auctionDetails),
              Tab(text: 'Live Bidding'),
              Tab(text: 'Auto Bid'),
              Tab(text: 'Seller Info'),
            ];

      tabViews = isOwner
          ? [
              _buildDetailsTab(auction, localizations),
              _buildLiveBiddingTab(auction),
              _buildSellerInfoTab(auction, localizations),
            ]
          : [
              _buildDetailsTab(auction, localizations),
              _buildLiveBiddingTab(auction),
              _buildAutoBidTab(auction),
              _buildSellerInfoTab(auction, localizations),
            ];
    }

    return Column(
      children: [
        TabBar(controller: _tabController!, tabs: tabs),
        SizedBox(
          height: 400,
          child: TabBarView(controller: _tabController!, children: tabViews),
        ),
      ],
    );
  }

  Widget _buildDetailsTab(Auction auction, AppLocalizations localizations) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (auction.description.isNotEmpty) ...[
            Text(
              localizations.description,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(auction.description, style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: AppConstants.defaultPadding),
          ],

          // Auction Details
          _buildDetailRow('Starting Price', '\$${auction.startingPrice.toStringAsFixed(2)}'),
          _buildDetailRow('Reserve Price', '\$${auction.reservePrice?.toStringAsFixed(2) ?? 'N/A'}'),
          _buildDetailRow('Bid Increment', '\$${auction.bidIncrement.toStringAsFixed(2)}'),
          // Location Information Section
          _buildLocationSection(auction),
          _buildDetailRow('Created', auction.formattedCreatedAt),

          if (auction.specialNotes?.isNotEmpty == true) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              localizations.specialNotes,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppColors.warning.withOpacity(0.3)),
              ),
              child: Text(auction.specialNotes ?? '', style: Theme.of(context).textTheme.bodyMedium),
            ),
          ],

          // Broker Services Section
          _buildBrokerServicesSection(auction),
        ],
      ),
    );
  }

  Widget _buildLocationSection(Auction auction) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Icon(Icons.location_on, size: 20, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'Location Information',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
              ),
            ],
          ),
        ),

        // Delivery Address (primary location)
        if (auction.deliveryAddress?.isNotEmpty == true)
          _buildDetailRow('Delivery Address', auction.deliveryAddress!)
        else
          _buildDetailRow('Delivery Address', 'Not specified'),

        // Catch Location (secondary information)
        _buildDetailRow('Catch Location', auction.location ?? 'Not specified'),

        // Live Location Status
        if (auction.hasLiveLocation) _buildDetailRow('Live Location', 'Available - Tap location icon to view'),

        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(label, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary)),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500)),
          ),
        ],
      ),
    );
  }

  Widget _buildBrokerServicesTab(Auction auction, AppLocalizations localizations) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isOwner = authProvider.user?.id == auction.seller.id;
    final userType = authProvider.userType;

    // Show different content based on user type
    if (isOwner) {
      return const Padding(
        padding: EdgeInsets.all(AppConstants.defaultPadding),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, size: 64, color: AppColors.textSecondary),
              SizedBox(height: 16),
              Text('Broker Services', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text(
                'Buyers can request broker services for this item',
                textAlign: TextAlign.center,
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      );
    }

    if (userType != 'buyer') {
      return const Padding(
        padding: EdgeInsets.all(AppConstants.defaultPadding),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.block, size: 64, color: AppColors.textSecondary),
              SizedBox(height: 16),
              Text('Access Restricted', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text(
                'Only buyers can request broker services',
                textAlign: TextAlign.center,
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      );
    }

    // For buyers, show the broker services content
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Consumer<BrokerProvider>(
        builder: (context, brokerProvider, child) {
          // Get current user's service requests for this auction
          final currentRequests = brokerProvider.myServiceRequests
              .where((req) => req.auctionId.toString() == auction.id.toString())
              .toList();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(Icons.support_agent, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'Broker Services',
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Description
              Text(
                'Request professional broker services for inspection, pickup, or shipping assistance.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              ),
              const SizedBox(height: 24),

              // Current requests (if any)
              if (currentRequests.isNotEmpty) ...[
                Text(
                  'Your Requests',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                ...currentRequests.map(
                  (request) => Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Service: ${request.service?.name ?? 'Unknown Service'}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Status: ${request.status}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Action buttons
              if (currentRequests.isEmpty) ...[
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _showBrokerServicesDialog(auction),
                    icon: const Icon(Icons.add_task),
                    label: const Text('Request Broker Service'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ] else ...[
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _showBrokerServicesDialog(auction),
                        icon: const Icon(Icons.add_task),
                        label: const Text('New Request'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => MyServiceRequestsScreen(filterAuctionId: auction.id.toString()),
                            ),
                          );
                        },
                        icon: const Icon(Icons.list),
                        label: const Text('View Requests'),
                        style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 12)),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  Widget _buildBrokerServicesSection(Auction auction) {
    // Only show if auction starts in 12+ hours and user is a buyer (not seller or broker)
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isOwner = authProvider.user?.id == auction.seller.id;
    final userType = authProvider.userType;

    // Only show for buyers
    if (isOwner || userType != 'buyer') return const SizedBox.shrink();

    // Check if auction ends in 12+ hours (show for scheduled and live auctions, hide when close to ending)
    final now = DateTime.now();
    if (auction.endTime == null) {
      return const SizedBox.shrink(); // Buy now auctions don't have end time
    }
    final timeUntilEnd = auction.endTime!.difference(now);

    if (timeUntilEnd.inHours < 12) {
      return const SizedBox.shrink();
    }

    return Consumer<BrokerProvider>(
      builder: (context, brokerProvider, child) {
        // Wait for data to load before filtering
        if (brokerProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // Get current user's service requests for this auction with flexible matching
        final currentRequests = brokerProvider.myServiceRequests
            .where((req) => req.auctionId.toString() == auction.id.toString())
            .toList();

        // Debug logging (can be removed in production)
        if (currentRequests.isNotEmpty) {
          print('Found ${currentRequests.length} broker request(s) for auction ${auction.id}');
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'طلب خدمات البروكر',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'يمكنك طلب خدمات البروكر للمعاينة والتفتيش والاستلام',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
            const SizedBox(height: AppConstants.smallPadding),

            // Show button to view current requests if any exist
            if (currentRequests.isNotEmpty) ...[
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: OutlinedButton.icon(
                  onPressed: () => _navigateToMyRequests(auction),
                  icon: const Icon(Icons.list_alt),
                  label: Text('عرض طلباتي (${currentRequests.length})'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.success,
                    side: BorderSide(color: AppColors.success),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppColors.primary.withOpacity(0.2)),
              ),
              child: Column(
                children: [
                  Icon(Icons.support_agent, size: 32, color: AppColors.primary),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    'خدمات متاحة',
                    style: Theme.of(
                      context,
                    ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold, color: AppColors.primary),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    '• المعاينة والتفتيش\n• الاستلام نيابة عن العميل\n• المعاينة أثناء التحميل\n• المساعدة في الشحن',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showBrokerServicesDialog(auction),
                      icon: const Icon(Icons.add_task),
                      label: Text(currentRequests.isNotEmpty ? 'طلب خدمة جديدة' : 'طلب خدمة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBiddingHistoryTab(AppLocalizations localizations) {
    return Consumer<AuctionProvider>(
      builder: (context, auctionProvider, child) {
        final bids = auctionProvider.selectedAuctionBids;

        if (bids.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.gavel_outlined, size: 48, color: AppColors.textHint),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  localizations.noBids,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textHint),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: bids.length,
          itemBuilder: (context, index) {
            final bid = bids[index];
            return ListTile(
              leading: CircleAvatar(
                backgroundColor: AppColors.primary.withOpacity(0.1),
                child: Text(
                  bid.bidderName[0].toUpperCase(),
                  style: const TextStyle(color: AppColors.primary, fontWeight: FontWeight.bold),
                ),
              ),
              title: Text(
                '\$${bid.amount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(bid.bidderName),
              trailing: Text(
                bid.formattedTimestamp,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildLiveBiddingTab(Auction auction) {
    return LiveBiddingHistory(auctionId: auction.id, showAutoBids: true);
  }

  Widget _buildAutoBidTab(Auction auction) {
    return AutoBidManager(auction: auction);
  }

  Widget _buildLocationMap(Auction auction) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.border),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Stack(
          children: [
            // Embedded OpenStreetMap view
            if (auction.latitude != null && auction.longitude != null)
              _buildEmbeddedMap(auction)
            else
              _buildMapPlaceholder(auction),

            // Action buttons overlay
            Positioned(
              top: AppConstants.smallPadding,
              right: AppConstants.smallPadding,
              child: Column(
                children: [
                  FloatingActionButton.small(
                    heroTag: 'refresh_location',
                    onPressed: () => _refreshSellerLocation(auction),
                    backgroundColor: Colors.white,
                    child: const Icon(Icons.refresh, color: AppColors.primary),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  FloatingActionButton.small(
                    heroTag: 'open_full_map',
                    onPressed: () => _openFullMapScreen(auction),
                    backgroundColor: Colors.white,
                    child: const Icon(Icons.fullscreen, color: AppColors.primary),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmbeddedMap(Auction auction) {
    // Add error handling for map initialization
    try {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: FlutterMap(
          options: MapOptions(
            initialCenter: LatLng(auction.latitude!, auction.longitude!),
            initialZoom: 15.0,
            minZoom: 5.0,
            maxZoom: 18.0,
          ),
          children: [
            // OpenStreetMap tile layer
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'com.fishauction.app',
              maxZoom: 18,
            ),
            // Seller marker
            MarkerLayer(
              markers: [
                Marker(
                  point: LatLng(auction.latitude!, auction.longitude!),
                  width: 40,
                  height: 40,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: const Icon(Icons.person_pin_circle, color: Colors.white, size: 24),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    } catch (e) {
      print('Error initializing map: $e');
      return _buildMapPlaceholder(auction);
    }
  }

  Widget _buildMapPlaceholder(Auction auction) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.background,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.map, size: 64, color: AppColors.primary),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Seller Location Map',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Location coordinates not available',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildNoLocationMessage() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.border),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.location_disabled, size: 64, color: AppColors.textHint),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Location Sharing Not Available',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: AppColors.textHint),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'The seller has not enabled live location sharing for this auction.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSellerInfoTab(Auction auction, AppLocalizations localizations) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Seller Avatar and Info
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppColors.primary.withOpacity(0.1),
                child: Text(
                  auction.sellerName[0].toUpperCase(),
                  style: const TextStyle(color: AppColors.primary, fontWeight: FontWeight.bold, fontSize: 24),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      auction.sellerName,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Verified Seller',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.success),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Contact Button
          PrimaryButton(text: 'Contact Seller', onPressed: () => _contactSeller(auction), icon: Icons.message),
        ],
      ),
    );
  }

  String _formatTimeRemaining(Duration duration) {
    if (duration.inSeconds <= 0) {
      return 'Auction Ended';
    } else if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h ${duration.inMinutes % 60}m';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m ${duration.inSeconds % 60}s';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m ${duration.inSeconds % 60}s';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  void _refreshSellerLocation(Auction auction) {
    // TODO: Implement location refresh
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Refreshing seller location...')));
  }

  void _openFullMapScreen(Auction auction) {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);

    // Check if user has bid on this auction
    if (!auctionProvider.hasUserBidOnAuction(auction.id)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('You must place a bid to view seller location'), backgroundColor: Colors.orange),
      );
      return;
    }

    // Always navigate to location screen - it will handle API calls and error states
    Navigator.push(context, MaterialPageRoute(builder: (context) => SellerLocationScreen(auction: auction)));
  }

  void _openInMaps(Auction auction) {
    if (auction.latitude != null && auction.longitude != null) {
      // TODO: Implement opening in maps app
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Opening in maps app...')));
    }
  }

  void _showBrokerServicesDialog(Auction auction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طلب خدمات البروكر'),
        content: const Text('سيتم توجيهك إلى صفحة طلب الخدمات'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToServiceRequest(auction);
            },
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  void _navigateToServiceRequest(Auction auction) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => ServiceRequestScreen(auction: auction)));
  }

  void _navigateToMyRequests(Auction auction) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            MyServiceRequestsScreen(filterAuctionId: auction.id.toString(), auctionTitle: auction.title),
      ),
    );
  }

  void _viewServiceRequestDetails(ServiceRequest request) {
    // Navigate to service request details or show quotes
    if (request.quotes.isNotEmpty) {
      _showQuotesDialog(request);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('لا توجد عروض متاحة حتى الآن')));
    }
  }

  void _showQuotesDialog(ServiceRequest request) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('عروض ${request.service?.nameAr ?? 'الخدمة'}'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: request.quotes.length,
            itemBuilder: (context, index) {
              final quote = request.quotes[index];
              return Card(
                child: ListTile(
                  title: Text('\$${quote.amount.toStringAsFixed(2)}'),
                  subtitle: Text('المدة المتوقعة: ${quote.estimatedDuration}'),
                  trailing: ElevatedButton(
                    onPressed: () => _selectBroker(request.id, quote.id),
                    child: const Text('اختيار'),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إغلاق'))],
      ),
    );
  }

  void _selectBroker(String serviceRequestId, String quoteId) async {
    final brokerProvider = Provider.of<BrokerProvider>(context, listen: false);
    final success = await brokerProvider.selectBroker(serviceRequestId, quoteId);

    if (mounted) {
      Navigator.of(context).pop(); // Close dialog

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('تم اختيار البروكر بنجاح'), backgroundColor: AppColors.success));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(brokerProvider.error ?? 'فشل في اختيار البروكر'), backgroundColor: AppColors.error),
        );
      }
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'quoted':
        return 'تم تقديم عروض';
      case 'accepted':
        return 'تم القبول';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return AppColors.warning;
      case 'quoted':
        return AppColors.info;
      case 'accepted':
      case 'in_progress':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  void _toggleWatchlist(Auction auction) {
    final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
    auctionProvider.toggleWatchlist(auction.id);
  }

  void _shareAuction(Auction auction) {
    final String shareText =
        '''
Check out this auction: ${auction.title}

${auction.description}

Current Price: \$${auction.currentPrice.toStringAsFixed(2)}
Fish Type: ${auction.fishType}
Quantity: ${auction.formattedQuantity}
Quality: ${auction.qualityGrade}

Ends: ${auction.endTime?.toString() ?? 'No end time (Buy Now)'}

Download Fish Auction App to bid!
''';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Auction'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy Link'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Copy auction link to clipboard
                ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Link copied to clipboard')));
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share via...'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Use platform share functionality
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Share functionality coming soon')));
              },
            ),
          ],
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel'))],
      ),
    );
  }

  void _showBidDialog(Auction auction, AppLocalizations localizations) {
    final TextEditingController bidController = TextEditingController();
    final double minBid = auction.currentPrice + auction.bidIncrement;

    // Check wallet balance requirement
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userWalletBalance = authProvider.user?.walletBalance ?? 0.0;
    final requiredBalance = auction.requiredWalletBalance;
    final canParticipate = auction.canParticipateWithBalance(userWalletBalance);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.placeBid),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${localizations.currentPrice}: \$${auction.currentPrice.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Minimum Bid: \$${minBid.toStringAsFixed(2)}',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Wallet balance warning if insufficient
            if (!canParticipate) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(Icons.warning, color: Colors.red.shade600, size: 24),
                    const SizedBox(height: 8),
                    Text(
                      'Insufficient Wallet Balance',
                      style: TextStyle(color: Colors.red.shade700, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'You need at least \$${requiredBalance.toStringAsFixed(2)} (10% of starting price) to participate in this auction.',
                      style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Your current balance: \$${userWalletBalance.toStringAsFixed(2)}',
                      style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            TextField(
              controller: bidController,
              keyboardType: TextInputType.number,
              enabled: canParticipate,
              decoration: InputDecoration(
                labelText: localizations.bidAmount,
                prefixText: '\$',
                border: const OutlineInputBorder(),
                hintText: canParticipate ? 'Minimum: \$${minBid.toStringAsFixed(2)}' : 'Insufficient wallet balance',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
          ElevatedButton(
            onPressed: canParticipate
                ? () async {
                    final bidAmount = double.tryParse(bidController.text);
                    if (bidAmount != null && bidAmount >= minBid) {
                      Navigator.of(context).pop();

                      // Use API bidding for proper authentication
                      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
                      final success = await auctionProvider.placeBid(auction.id, bidAmount);
                      if (mounted) {
                        if (success) {
                          // Refresh auction data and bidding history
                          await auctionProvider.loadAuctionDetails(auction.id);

                          ScaffoldMessenger.of(
                            context,
                          ).showSnackBar(const SnackBar(content: Text('Bid placed successfully!')));
                        } else {
                          ScaffoldMessenger.of(
                            context,
                          ).showSnackBar(SnackBar(content: Text(auctionProvider.error ?? 'Failed to place bid')));
                        }
                      }
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Please enter a valid bid amount (minimum \$${minBid.toStringAsFixed(2)})'),
                        ),
                      );
                    }
                  }
                : null,
            child: Text(localizations.placeBid),
          ),
        ],
      ),
    );
  }

  void _showAutoBidDialog(Auction auction, AppLocalizations localizations) {
    final TextEditingController maxAmountController = TextEditingController();
    final double minAmount = auction.currentPrice + auction.bidIncrement;

    // Check wallet balance requirement
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userWalletBalance = authProvider.user?.walletBalance ?? 0.0;
    final requiredBalance = auction.requiredWalletBalance;
    final canParticipate = auction.canParticipateWithBalance(userWalletBalance);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.autoBid),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${localizations.currentPrice}: \$${auction.currentPrice.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Minimum Amount: \$${minAmount.toStringAsFixed(2)}',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Wallet balance warning if insufficient
            if (!canParticipate) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Icon(Icons.warning, color: Colors.red.shade600, size: 24),
                    const SizedBox(height: 8),
                    Text(
                      'Insufficient Wallet Balance',
                      style: TextStyle(color: Colors.red.shade700, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'You need at least \$${requiredBalance.toStringAsFixed(2)} (10% of starting price) to participate in this auction.',
                      style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Your current balance: \$${userWalletBalance.toStringAsFixed(2)}',
                      style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            TextField(
              controller: maxAmountController,
              keyboardType: TextInputType.number,
              enabled: canParticipate,
              decoration: InputDecoration(
                labelText: 'Maximum Bid Amount',
                prefixText: '\$',
                border: const OutlineInputBorder(),
                helperText: canParticipate
                    ? 'We will automatically bid up to this amount'
                    : 'Insufficient wallet balance to participate',
                hintText: canParticipate ? 'Minimum: \$${minAmount.toStringAsFixed(2)}' : 'Cannot participate',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
          ElevatedButton(
            onPressed: canParticipate
                ? () async {
                    final maxAmount = double.tryParse(maxAmountController.text);
                    if (maxAmount != null && maxAmount >= minAmount) {
                      Navigator.of(context).pop();
                      final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
                      final success = await auctionProvider.setAutoBid(auction.id, maxAmount);
                      if (mounted) {
                        if (success) {
                          ScaffoldMessenger.of(
                            context,
                          ).showSnackBar(const SnackBar(content: Text('Auto bid set successfully!')));
                        } else {
                          ScaffoldMessenger.of(
                            context,
                          ).showSnackBar(SnackBar(content: Text(auctionProvider.error ?? 'Failed to set auto bid')));
                        }
                      }
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Please enter a valid amount (minimum \$${minAmount.toStringAsFixed(2)})'),
                        ),
                      );
                    }
                  }
                : null,
            child: Text(localizations.autoBid),
          ),
        ],
      ),
    );
  }

  void _contactSeller(Auction auction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact ${auction.sellerName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.phone),
              title: const Text('Call Seller'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement phone call functionality
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Phone call feature coming soon')));
              },
            ),
            ListTile(
              leading: const Icon(Icons.message),
              title: const Text('Send Message'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement messaging functionality
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Messaging feature coming soon')));
              },
            ),
            ListTile(
              leading: const Icon(Icons.email),
              title: const Text('Send Email'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Implement email functionality
                ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Email feature coming soon')));
              },
            ),
          ],
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel'))],
      ),
    );
  }

  void _showBuyNowDialog(Auction auction, AppLocalizations localizations) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Buy Now'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to buy this item for \$${auction.currentPrice.toStringAsFixed(2)}?',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    'Total Amount',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${auction.currentPrice.toStringAsFixed(2)}',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(color: AppColors.primary, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processBuyNow(auction);
            },
            child: const Text('Buy Now'),
          ),
        ],
      ),
    );
  }

  void _processBuyNow(Auction auction) async {
    try {
      // Import payment service
      final PaymentService paymentService = PaymentService();
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      setState(() => _isLoading = true);

      // 1. Check wallet balance
      final balanceResponse = await paymentService.getWalletBalance();
      if (!balanceResponse.isSuccess) {
        throw Exception('Failed to check wallet balance');
      }

      final walletBalance = balanceResponse.data!;
      final requiredAmount = auction.currentPrice;

      // 2. Check if sufficient funds
      if (walletBalance < requiredAmount) {
        setState(() => _isLoading = false);
        _showAddFundsDialog(auction, walletBalance, requiredAmount);
        return;
      }

      // 3. Process payment using wallet balance
      final paymentResponse = await paymentService.processPayment(auctionId: auction.id, paymentMethod: 'wallet');

      if (paymentResponse.isSuccess) {
        // 4. Update auth provider balance
        await authProvider.refreshProfile();

        // 5. Navigate to earned auction screen (same as winning an auction)
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Purchase successful! Redirecting to delivery tracking...'),
              backgroundColor: AppColors.success,
            ),
          );

          // Navigate to earned auction screen for payment/delivery tracking
          Navigator.of(
            context,
          ).pushReplacement(MaterialPageRoute(builder: (context) => EarnedAuctionScreen(auction: auction)));
        }
      } else {
        throw Exception(paymentResponse.message ?? 'Payment failed');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Purchase failed: ${e.toString()}'), backgroundColor: AppColors.error));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showAddFundsDialog(Auction auction, double walletBalance, double requiredAmount) {
    final shortfall = requiredAmount - walletBalance;
    final suggestedAmount = (shortfall + 10).ceilToDouble();
    final amountController = TextEditingController(text: suggestedAmount.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.account_balance_wallet, color: AppColors.error),
            SizedBox(width: 8),
            Text('Insufficient Funds'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You need \$${requiredAmount.toStringAsFixed(2)} to purchase this item.'),
            const SizedBox(height: 8),
            Text('Current balance: \$${walletBalance.toStringAsFixed(2)}'),
            Text('Required: \$${shortfall.toStringAsFixed(2)} more'),
            const SizedBox(height: 16),
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Amount to add',
                prefixText: '\$',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processAddFunds(auction, amountController.text);
            },
            child: const Text('Add Funds'),
          ),
        ],
      ),
    );
  }

  Future<void> _processAddFunds(Auction auction, String amountText) async {
    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a valid amount'), backgroundColor: AppColors.error));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final PaymentService paymentService = PaymentService();
      final response = await paymentService.topUpWalletWithStripe(amount: amount, context: context);

      if (response.isSuccess) {
        // Refresh auth provider balance
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        await authProvider.refreshProfile();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Funds added successfully! You can now complete your purchase.'),
              backgroundColor: AppColors.success,
            ),
          );

          // Retry the purchase
          _processBuyNow(auction);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response.message ?? 'Failed to add funds'), backgroundColor: AppColors.error),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding funds: ${e.toString()}'), backgroundColor: AppColors.error),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
