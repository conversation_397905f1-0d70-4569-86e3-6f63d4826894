import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/payment_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/payment_service.dart';
import '../../services/api_service.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/loading_overlay.dart';
import '../profile/payment_methods_screen.dart';
import '../profile/withdrawal_requests_screen.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  final PaymentService _paymentService = PaymentService();
  bool _isLoading = false;
  List<WalletTransaction> _recentTransactions = [];

  @override
  void initState() {
    super.initState();
    _loadRecentTransactions();
  }

  Future<void> _loadRecentTransactions() async {
    try {
      final response = await _paymentService.getWalletTransactions(limit: 5);
      if (response.isSuccess && response.data != null) {
        setState(() {
          _recentTransactions = response.data!;
        });
      }
    } catch (e) {
      debugPrint('Error loading recent transactions: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.wallet),
        actions: [IconButton(onPressed: _showTransactionHistory, icon: const Icon(Icons.history))],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  // Balance Card
                  _buildBalanceCard(authProvider, localizations),

                  const SizedBox(height: AppConstants.largePadding),

                  // Quick Actions
                  _buildQuickActions(localizations),

                  const SizedBox(height: AppConstants.largePadding),

                  // Recent Transactions
                  _buildRecentTransactions(localizations),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBalanceCard(AuthProvider authProvider, AppLocalizations localizations) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: AppColors.primaryGradient,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [BoxShadow(color: AppColors.shadow, blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.walletBalance,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.textLight.withOpacity(0.9)),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            '\$${authProvider.walletBalance.toStringAsFixed(2)}',
            style: Theme.of(
              context,
            ).textTheme.displayMedium?.copyWith(color: AppColors.textLight, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Row(
            children: [
              Icon(Icons.account_balance_wallet, color: AppColors.textLight.withOpacity(0.8), size: 16),
              const SizedBox(width: 4),
              Text(
                'Available Balance',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textLight.withOpacity(0.8)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Quick Actions', style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: AppConstants.defaultPadding),
        Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    icon: Icons.add,
                    title: localizations.addFunds,
                    subtitle: 'Top up your wallet',
                    color: AppColors.success,
                    onTap: _showAddFundsDialog,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildActionCard(
                    icon: Icons.remove,
                    title: localizations.withdrawFunds,
                    subtitle: 'Withdraw to bank',
                    color: AppColors.warning,
                    onTap: _showWithdrawDialog,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildActionCard(
              icon: Icons.history,
              title: 'Withdrawal Requests',
              subtitle: 'View withdrawal status',
              color: AppColors.info,
              onTap: _navigateToWithdrawalRequests,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: AppColors.border),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(color: color.withOpacity(0.1), shape: BoxShape.circle),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Transactions',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(onPressed: _showTransactionHistory, child: Text('View All')),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.border),
          ),
          child: _recentTransactions.isEmpty
              ? Padding(
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.receipt_long_outlined, size: 48, color: AppColors.textHint),
                        const SizedBox(height: AppConstants.smallPadding),
                        Text(
                          'No transactions yet',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.textHint),
                        ),
                      ],
                    ),
                  ),
                )
              : Column(children: _recentTransactions.map((transaction) => _buildTransactionItem(transaction)).toList()),
        ),
      ],
    );
  }

  Widget _buildTransactionItem(WalletTransaction transaction) {
    // Determine if transaction is credit or debit based on amount value
    final isCredit = transaction.amount > 0;
    final color = isCredit ? AppColors.success : AppColors.error;
    final icon = isCredit ? Icons.add_circle_outline : Icons.remove_circle_outline;
    final sign = isCredit ? '+' : '-';

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.border, width: 0.5)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(color: color.withValues(alpha: 0.1), shape: BoxShape.circle),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.transactionTypeDisplay,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 2),
                Text(
                  transaction.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textHint),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  _formatDateTime(transaction.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textHint),
                ),
              ],
            ),
          ),
          Text(
            '$sign\$${transaction.amount.abs().toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'Today ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _showAddFundsDialog() {
    final localizations = AppLocalizations.of(context);
    final amountController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.add_card, color: AppColors.primary),
            const SizedBox(width: AppConstants.smallPadding),
            Text(localizations.addFunds),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                labelText: localizations.amount,
                prefixText: '\$',
                hintText: '10.00',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              padding: const EdgeInsets.all(AppConstants.smallPadding),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.info.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, color: AppColors.info, size: 20),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Text(
                      'You will be redirected to Stripe\'s secure payment form',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.info),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _processStripePayment(amountController.text);
            },
            icon: const Icon(Icons.payment),
            label: const Text('Pay with Stripe'),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary, foregroundColor: Colors.white),
          ),
        ],
      ),
    );
  }

  // Process Stripe payment directly with native payment sheet
  Future<void> _processStripePayment(String amount) async {
    final double? amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context).pleaseEnterValidAmount), backgroundColor: AppColors.error),
      );
      return;
    }

    if (amountValue < 1.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.of(context).minimumTopupAmount), backgroundColor: AppColors.error),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Use Stripe service to process payment with native payment sheet
      final response = await _paymentService.topUpWalletWithStripe(amount: amountValue, context: context);

      if (response.isSuccess) {
        // Refresh the profile to get updated wallet balance from backend
        try {
          if (mounted) {
            final authProvider = Provider.of<AuthProvider>(context, listen: false);
            await authProvider.refreshProfile();
          }
        } catch (e) {
          debugPrint('Profile refresh failed: $e');
        }

        if (mounted) {
          // Reload recent transactions to show the new transaction
          _loadRecentTransactions();

          // Show success message with new balance if available
          final newBalance = response.data?['new_balance'];
          final successMessage = newBalance != null
              ? 'Payment successful! \$${amountValue.toStringAsFixed(2)} added to your wallet. New balance: \$${newBalance.toStringAsFixed(2)}'
              : 'Payment successful! \$${amountValue.toStringAsFixed(2)} added to your wallet.';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response.message ?? 'Payment failed'), backgroundColor: AppColors.error),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error processing payment: ${e.toString()}'), backgroundColor: AppColors.error),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showWithdrawDialog() async {
    // First ensure auth tokens are loaded
    await ApiService().loadAuthTokens();

    // First check if user has payment methods
    setState(() {
      _isLoading = true;
    });

    try {
      print('🔍 Checking payout methods for withdrawal...');
      print('🔐 Auth status: ${ApiService().authStatus}');
      final response = await _paymentService.checkPayoutMethods();
      print('📡 API Response: ${response.isSuccess ? 'SUCCESS' : 'FAILED'}');
      if (response.data != null) {
        print('📊 Response data: ${response.data}');
      } else {
        print('❌ No response data: ${response.message}');
      }

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (response.isSuccess) {
        final data = response.data!;
        final hasPayoutMethods = data['has_payout_methods'] ?? false;

        if (!hasPayoutMethods) {
          // Show dialog to add payment method first
          _showAddPaymentMethodDialog();
          return;
        }

        // User has payment methods, show payment method selection
        _showPaymentMethodSelectionDialog();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? 'Failed to check payment methods'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
    }
  }

  void _showAddPaymentMethodDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Payment Method'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.payment, size: 64, color: AppColors.warning),
            SizedBox(height: AppConstants.defaultPadding),
            Text('You need to add a payment method before you can withdraw funds.', textAlign: TextAlign.center),
            SizedBox(height: AppConstants.smallPadding),
            Text(
              'Go to Profile > Payment Methods to add your preferred withdrawal method.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(AppLocalizations.of(context).cancel)),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToPaymentMethods();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: Text(AppLocalizations.of(context).addPaymentMethod),
          ),
        ],
      ),
    );
  }

  void _navigateToPaymentMethods() {
    // Import the payment methods screen
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const PaymentMethodsScreen()));
  }

  void _navigateToWithdrawalRequests() {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => const WithdrawalRequestsScreen()));
  }

  void _showPaymentMethodSelectionDialog() async {
    // Load payment methods
    final response = await _paymentService.getPayoutMethods();
    if (!response.isSuccess || response.data == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load payment methods: ${response.message}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      return;
    }

    final paymentMethods = response.data!;

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Payment Method'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Choose a payment method for withdrawal:'),
              const SizedBox(height: 16),
              ...paymentMethods.map(
                (method) => Card(
                  child: ListTile(
                    leading: Icon(_getPaymentMethodIcon(method['payout_type']), color: AppColors.primary),
                    title: Text(_getPaymentMethodTitle(method)),
                    subtitle: Text(_getPaymentMethodSubtitle(method)),
                    trailing: method['is_verified']
                        ? const Icon(Icons.verified, color: AppColors.success)
                        : const Icon(Icons.pending, color: AppColors.warning),
                    onTap: () {
                      if (!method['is_verified']) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('This payment method is not verified yet. Please wait for admin approval.'),
                            backgroundColor: AppColors.warning,
                          ),
                        );
                        return;
                      }
                      Navigator.of(context).pop();
                      _showWithdrawAmountDialog(method);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToPaymentMethods();
            },
            child: const Text('Manage Methods'),
          ),
        ],
      ),
    );
  }

  IconData _getPaymentMethodIcon(String type) {
    switch (type) {
      case 'paypal':
        return Icons.payment;
      case 'stripe':
        return Icons.credit_card;
      case 'bank_transfer':
        return Icons.account_balance;
      case 'payoneer':
        return Icons.account_balance_wallet;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodTitle(Map<String, dynamic> method) {
    switch (method['payout_type']) {
      case 'paypal':
        return 'PayPal';
      case 'stripe':
        return 'Stripe';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'payoneer':
        return 'Payoneer';
      default:
        return method['payout_type'].toString().toUpperCase();
    }
  }

  String _getPaymentMethodSubtitle(Map<String, dynamic> method) {
    switch (method['payout_type']) {
      case 'paypal':
        return method['paypal_email'] ?? 'No email';
      case 'stripe':
        return 'Account: ${method['stripe_account_id'] ?? 'Not set'}';
      case 'bank_transfer':
        return method['bank_name'] ?? 'Bank account';
      case 'payoneer':
        return method['payoneer_email'] ?? 'No email';
      default:
        return 'Payment method';
    }
  }

  void _showWithdrawAmountDialog([Map<String, dynamic>? selectedPaymentMethod]) {
    final localizations = AppLocalizations.of(context);
    final amountController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.withdrawFunds),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(labelText: localizations.amount, prefixText: '\$', hintText: '0.00'),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Funds will be transferred to your default payment method. A \$2 processing fee will be deducted.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(localizations.cancel)),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processWithdrawRequest(amountController.text, selectedPaymentMethod);
            },
            child: const Text('Withdraw'),
          ),
        ],
      ),
    );
  }

  void _showTransactionHistory() {
    // TODO: Navigate to transaction history screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Transaction history feature coming soon')));
  }

  void _processWithdrawRequest(String amount, Map<String, dynamic>? paymentMethod) async {
    if (amount.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter an amount'), backgroundColor: AppColors.error));
      return;
    }

    if (paymentMethod == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please select a payment method'), backgroundColor: AppColors.error));
      return;
    }

    final double? withdrawAmount = double.tryParse(amount);
    if (withdrawAmount == null || withdrawAmount <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a valid amount'), backgroundColor: AppColors.error));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create withdrawal request with selected payment method
      final response = await _paymentService.requestWithdrawal(
        amount: withdrawAmount,
        payoutMethodId: paymentMethod['id'],
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.isSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Withdrawal request submitted successfully! It will be processed by admin.'),
              backgroundColor: AppColors.success,
            ),
          );
          // Refresh recent transactions
          _loadRecentTransactions();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? 'Failed to submit withdrawal request'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: AppColors.error));
      }
    }
  }

  void _processWithdraw(String amount) async {
    if (amount.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Please enter an amount'), backgroundColor: AppColors.error));
      return;
    }

    final double? amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue <= 0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Please enter a valid amount'), backgroundColor: AppColors.error));
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (amountValue > authProvider.walletBalance) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Insufficient balance'), backgroundColor: AppColors.error));
      return;
    }

    if (amountValue < 10.0) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Minimum withdrawal amount is \$10.00'), backgroundColor: AppColors.error));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Withdraw from wallet
      final response = await _paymentService.withdrawFromWallet(amount: amountValue);

      if (response.isSuccess) {
        // Refresh wallet balance
        await authProvider.refreshProfile();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Withdrawal request submitted successfully!'), backgroundColor: AppColors.success),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(response.message ?? 'Withdrawal failed'), backgroundColor: AppColors.error),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error processing withdrawal: ${e.toString()}'), backgroundColor: AppColors.error),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Debug method to check auth status
  void _debugAuthStatus() async {
    print('🔍 DEBUG: Checking auth status...');
    print('🔐 API Service auth status: ${ApiService().authStatus}');
    print('🔐 API Service is authenticated: ${ApiService().isAuthenticated}');

    // Try to make a simple API call to test authentication
    try {
      final response = await _paymentService.checkPayoutMethods();
      print('📡 Test API call result: ${response.isSuccess ? 'SUCCESS' : 'FAILED'}');
      if (response.isSuccess) {
        print('📊 Response data: ${response.data}');
      } else {
        print('❌ Error: ${response.message}');
      }

      // Show result to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Auth Status: ${ApiService().isAuthenticated ? 'Authenticated' : 'Not Authenticated'}\nAPI Call: ${response.isSuccess ? 'Success' : 'Failed'}',
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('❌ Exception during API call: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e'), backgroundColor: AppColors.error));
      }
    }
  }
}
