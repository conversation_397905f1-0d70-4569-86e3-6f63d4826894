import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/payment_service.dart';
import '../../services/delivery_service.dart';
import '../../models/delivery_model.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/loading_overlay.dart';
import 'invoice_screen.dart';

class EarnedAuctionScreen extends StatefulWidget {
  final Auction auction;

  const EarnedAuctionScreen({super.key, required this.auction});

  @override
  State<EarnedAuctionScreen> createState() => _EarnedAuctionScreenState();
}

class _EarnedAuctionScreenState extends State<EarnedAuctionScreen> {
  final PaymentService _paymentService = PaymentService();
  final DeliveryService _deliveryService = DeliveryService();
  bool _isLoading = false;
  double _walletBalance = 0.0;
  Timer? _countdownTimer;
  Timer? _deliveryUpdateTimer;
  Duration _remainingTime = Duration.zero;
  bool _isPaymentCompleted = false;
  int _deliveryStep = 0;
  String _currentDeliveryStatus = 'pending';
  List<DeliveryUpdate> _deliveryUpdates = [];

  // Delivery steps - matching seller's exact flow
  final List<DeliveryStep> _deliverySteps = [
    DeliveryStep(
      title: 'Payment Confirmed',
      description: 'Your payment has been processed successfully',
      icon: Icons.payment,
    ),
    DeliveryStep(
      title: 'Preparing for Delivery',
      description: 'Seller is preparing your fish for delivery',
      icon: Icons.inventory,
    ),
    DeliveryStep(title: 'Picked Up', description: 'Seller has picked up your order', icon: Icons.local_shipping),
    DeliveryStep(title: 'In Transit', description: 'Your order is being transported', icon: Icons.directions_car),
    DeliveryStep(
      title: 'Out for Delivery',
      description: 'Your order is out for final delivery',
      icon: Icons.delivery_dining,
    ),
    DeliveryStep(
      title: 'Delivered',
      description: 'Your fish has been delivered successfully',
      icon: Icons.check_circle,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _checkPaymentStatus();
    if (!_isPaymentCompleted) {
      _loadWalletBalance();
      _initializeCountdown();
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _deliveryUpdateTimer?.cancel();
    super.dispose();
  }

  void _checkPaymentStatus() {
    // Check if payment is already completed
    _isPaymentCompleted = widget.auction.paymentReceived;

    if (_isPaymentCompleted) {
      _loadDeliveryStatus();
      _startDeliveryUpdateTimer();
    }
  }

  void _loadDeliveryStatus() async {
    try {
      print('📦 Loading delivery status for auction: ${widget.auction.id}');

      // Get delivery tracking for this auction from backend
      final trackingResponse = await _deliveryService.getDeliveryTracking(widget.auction.id);
      if (trackingResponse.isSuccess && trackingResponse.data != null) {
        final delivery = trackingResponse.data!;
        print('✅ Delivery found: ${delivery.id}, Status: ${delivery.status}');

        // Get delivery updates from backend
        final updatesResponse = await _deliveryService.getDeliveryUpdates(delivery.id);
        if (updatesResponse.isSuccess && updatesResponse.data != null) {
          print('✅ Delivery updates found: ${updatesResponse.data!.length} updates');
          setState(() {
            _deliveryUpdates = updatesResponse.data!;
            _currentDeliveryStatus = delivery.status;
            _updateDeliveryStep();
          });
        } else {
          print('⚠️ No delivery updates found, using delivery status: ${delivery.status}');
          // Use delivery status from tracking
          setState(() {
            _currentDeliveryStatus = delivery.status;
            _updateDeliveryStepFromStatus(delivery.status);
          });
        }
      } else {
        print('⚠️ No delivery found yet - showing default status');
        // No delivery found yet - show default status
        setState(() {
          _deliveryStep = 1; // Default to "Preparing for Delivery"
          _currentDeliveryStatus = 'pending';
        });
      }
    } catch (e) {
      print('❌ Error loading delivery status: $e');
      // Error loading - show default status
      setState(() {
        _deliveryStep = 1; // Default to "Preparing for Delivery"
        _currentDeliveryStatus = 'pending';
      });
    }
  }

  Future<void> _refreshDeliveryStatus() async {
    _loadDeliveryStatus();
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'PREPARING';
      case 'picked_up':
        return 'PICKED UP';
      case 'in_transit':
        return 'IN TRANSIT';
      case 'out_for_delivery':
        return 'OUT FOR DELIVERY';
      case 'delivered':
        return 'DELIVERED';
      default:
        return status.toUpperCase();
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'picked_up':
        return Colors.blue;
      case 'in_transit':
        return Colors.purple;
      case 'out_for_delivery':
        return AppColors.primary;
      case 'delivered':
        return AppColors.success;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.schedule;
      case 'picked_up':
        return Icons.local_shipping;
      case 'in_transit':
        return Icons.directions_car;
      case 'out_for_delivery':
        return Icons.delivery_dining;
      case 'delivered':
        return Icons.check_circle;
      default:
        return Icons.info;
    }
  }

  String _getDetailedStatusDescription(String status) {
    switch (status) {
      case 'pending':
        return 'Seller is preparing your order for delivery';
      case 'picked_up':
        return 'Seller has picked up your order and is preparing for delivery';
      case 'in_transit':
        return 'Your order is currently in transit to the delivery location';
      case 'out_for_delivery':
        return 'Your order is out for delivery and will arrive soon';
      case 'delivered':
        return 'Your order has been successfully delivered';
      default:
        return 'Status information not available';
    }
  }

  void _updateDeliveryStep() {
    if (_deliveryUpdates.isEmpty) {
      _updateDeliveryStepFromStatus(_currentDeliveryStatus);
      return;
    }

    // Get the latest delivery status
    final latestUpdate = _deliveryUpdates.first;
    _updateDeliveryStepFromStatus(latestUpdate.status);
  }

  void _updateDeliveryStepFromStatus(String status) {
    print('🔄 Updating delivery step from status: $status');
    switch (status) {
      case 'pending':
        _deliveryStep = 1; // Preparing for Delivery
        break;
      case 'picked_up':
        _deliveryStep = 2; // Picked Up
        break;
      case 'in_transit':
        _deliveryStep = 3; // In Transit
        break;
      case 'out_for_delivery':
        _deliveryStep = 4; // Out for Delivery
        break;
      case 'delivered':
        _deliveryStep = 5; // Delivered
        break;
      default:
        _deliveryStep = 1; // Default to Preparing for Delivery
    }
    print('📍 Updated delivery step to: $_deliveryStep for status: $status');
  }

  void _startDeliveryUpdateTimer() {
    _deliveryUpdateTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        print('🔄 Auto-refreshing delivery status...');
        _loadDeliveryStatus();
      } else {
        timer.cancel();
      }
    });
  }

  void _initializeCountdown() {
    if (widget.auction.paymentDeadline != null) {
      final now = DateTime.now();
      final deadline = widget.auction.paymentDeadline!;

      if (deadline.isAfter(now)) {
        _remainingTime = deadline.difference(now);
        _startCountdown();
      } else {
        _remainingTime = Duration.zero;
        _handlePaymentTimeout();
      }
    }
  }

  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime.inSeconds > 0) {
          _remainingTime = _remainingTime - const Duration(seconds: 1);
        } else {
          timer.cancel();
          _handlePaymentTimeout();
        }
      });
    });
  }

  void _handlePaymentTimeout() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.timer_off, color: AppColors.error),
            SizedBox(width: 8),
            Text('Payment Timeout'),
          ],
        ),
        content: const Text(
          'Your payment window has expired. The auction has been reassigned to the next highest bidder.',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to auctions
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _loadWalletBalance() async {
    setState(() => _isLoading = true);

    try {
      final response = await _paymentService.getWalletBalance();
      if (response.isSuccess) {
        setState(() => _walletBalance = response.data!);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to load wallet balance');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isPaymentCompleted ? 'Delivery Tracking' : 'Complete Payment'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_isPaymentCompleted) IconButton(onPressed: _showDeliveryInfo, icon: const Icon(Icons.info_outline)),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: _isPaymentCompleted ? _buildDeliveryView() : _buildPaymentView(),
        ),
      ),
    );
  }

  Widget _buildPaymentView() {
    final hasInsufficientFunds = _walletBalance < widget.auction.currentPrice;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Countdown Timer Card
        _buildCountdownCard(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Auction Summary Card
        _buildAuctionSummaryCard(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Wallet Balance Card
        _buildWalletBalanceCard(hasInsufficientFunds),

        const SizedBox(height: AppConstants.largePadding),

        // Payment Button
        _buildPaymentButton(hasInsufficientFunds),
      ],
    );
  }

  Widget _buildDeliveryView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Success Message Card
        _buildSuccessCard(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Auction Summary Card
        _buildAuctionSummaryCard(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Delivery Progress
        _buildDeliveryProgress(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Seller Contact Card
        _buildSellerContactCard(),

        const SizedBox(height: AppConstants.defaultPadding),

        // Action Buttons
        _buildDeliveryActionButtons(),
      ],
    );
  }

  Widget _buildCountdownCard() {
    final minutes = _remainingTime.inMinutes;
    final seconds = _remainingTime.inSeconds % 60;
    final isUrgent = _remainingTime.inMinutes < 5;

    return Card(
      color: isUrgent ? AppColors.error.withOpacity(0.1) : AppColors.primary.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.timer, color: isUrgent ? AppColors.error : AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Payment Deadline',
                  style: TextStyle(fontWeight: FontWeight.bold, color: isUrgent ? AppColors.error : AppColors.primary),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: isUrgent ? AppColors.error : AppColors.primary,
              ),
            ),
            Text(
              isUrgent ? 'Hurry up! Payment expires soon' : 'Complete payment within this time',
              style: TextStyle(fontSize: 12, color: isUrgent ? AppColors.error : Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessCard() {
    return Card(
      color: AppColors.success.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            const Icon(Icons.check_circle, color: AppColors.success, size: 32),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Payment Successful!',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppColors.success),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Your payment of \$${widget.auction.currentPrice.toStringAsFixed(2)} has been processed.',
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuctionSummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _isPaymentCompleted ? 'Your Purchase' : 'Auction Summary',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    widget.auction.images.isNotEmpty ? widget.auction.images.first : '',
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 80,
                      height: 80,
                      color: Colors.grey[300],
                      child: const Icon(Icons.image_not_supported),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(widget.auction.title, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.auction.fishType} • ${widget.auction.quantity}${widget.auction.unit}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                      if (_isPaymentCompleted) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Location: ${widget.auction.location ?? 'Not specified'}',
                          style: TextStyle(color: Colors.grey[600], fontSize: 12),
                        ),
                      ],
                      const SizedBox(height: 8),
                      Text(
                        '${_isPaymentCompleted ? 'Paid' : 'Winning Bid'}: \$${widget.auction.currentPrice.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppColors.primary),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletBalanceCard(bool hasInsufficientFunds) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.account_balance_wallet, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Wallet Balance', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Current Balance:'),
                Text(
                  '\$${_walletBalance.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Required Amount:'),
                Text(
                  '\$${widget.auction.currentPrice.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            if (hasInsufficientFunds) ...[
              const Divider(),
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: AppColors.error),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Insufficient Balance',
                            style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.error),
                          ),
                          Text(
                            'You need \$${(widget.auction.currentPrice - _walletBalance).toStringAsFixed(2)} more',
                            style: const TextStyle(fontSize: 12, color: AppColors.error),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentButton(bool hasInsufficientFunds) {
    if (hasInsufficientFunds) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showAddFundsDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add Funds to Wallet'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: null,
              style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)), // Disabled
              child: const Text('Confirm Payment'),
            ),
          ),
        ],
      );
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _confirmPayment,
        icon: const Icon(Icons.payment),
        label: const Text('Confirm Payment'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  Widget _buildDeliveryProgress() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text('Delivery Progress', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const Spacer(),
                // Refresh button
                IconButton(
                  onPressed: _refreshDeliveryStatus,
                  icon: const Icon(Icons.refresh, size: 20),
                  tooltip: 'Refresh delivery status',
                ),
                // Show current status badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_currentDeliveryStatus),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusDisplayName(_currentDeliveryStatus),
                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            // Current status description
            if (_currentDeliveryStatus != 'pending')
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: _getStatusColor(_currentDeliveryStatus).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _getStatusColor(_currentDeliveryStatus).withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getStatusIcon(_currentDeliveryStatus),
                      color: _getStatusColor(_currentDeliveryStatus),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current Status: ${_getStatusDisplayName(_currentDeliveryStatus)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getStatusColor(_currentDeliveryStatus),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getDetailedStatusDescription(_currentDeliveryStatus),
                            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ...List.generate(_deliverySteps.length, (index) {
              final step = _deliverySteps[index];
              final isActive = index == _deliveryStep;
              final isCompleted = index < _deliveryStep;
              final isLast = index == _deliverySteps.length - 1;

              return Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isCompleted
                              ? AppColors.success
                              : isActive
                              ? AppColors.primary
                              : Colors.grey[300],
                        ),
                        child: Icon(
                          step.icon,
                          color: isCompleted || isActive ? Colors.white : Colors.grey[600],
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: AppConstants.defaultPadding),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              step.title,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: isCompleted
                                    ? AppColors.success
                                    : isActive
                                    ? AppColors.primary
                                    : Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(step.description, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                          ],
                        ),
                      ),
                      if (isCompleted) const Icon(Icons.check, color: AppColors.success),
                    ],
                  ),
                  if (!isLast)
                    Container(
                      margin: const EdgeInsets.only(left: 20, top: 8, bottom: 8),
                      height: 30,
                      width: 2,
                      color: isCompleted ? AppColors.success : Colors.grey[300],
                    ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildSellerContactCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Seller Information', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: Text(
                    widget.auction.seller.username[0].toUpperCase(),
                    style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.auction.seller.username,
                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      const SizedBox(height: 2),
                      const Text('Seller', style: TextStyle(color: Colors.grey, fontSize: 12)),
                    ],
                  ),
                ),
                IconButton(onPressed: _contactSeller, icon: const Icon(Icons.message), tooltip: 'Contact Seller'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _trackDelivery,
            icon: const Icon(Icons.location_on),
            label: const Text('Track Live Location'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _viewReceipt,
            icon: const Icon(Icons.receipt),
            label: const Text('View Receipt'),
            style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)),
          ),
        ),
      ],
    );
  }

  void _showAddFundsDialog() {
    final requiredAmount = widget.auction.currentPrice - _walletBalance;
    final suggestedAmount = (requiredAmount + 10).ceilToDouble();
    final amountController = TextEditingController(text: suggestedAmount.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.add_card, color: AppColors.primary),
            SizedBox(width: 8),
            Text('Add Funds'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('You need at least \$${requiredAmount.toStringAsFixed(2)} more to complete this payment.'),
            const SizedBox(height: 16),
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Amount to Add',
                prefixText: '\$',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processAddFunds(amountController.text);
            },
            child: const Text('Add Funds'),
          ),
        ],
      ),
    );
  }

  Future<void> _processAddFunds(String amount) async {
    final double? amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue <= 0) {
      _showErrorSnackBar('Please enter a valid amount');
      return;
    }

    final requiredAmount = widget.auction.currentPrice - _walletBalance;
    if (amountValue < requiredAmount) {
      _showErrorSnackBar('Amount must be at least \$${requiredAmount.toStringAsFixed(2)}');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await _paymentService.topUpWalletWithStripe(amount: amountValue, context: context);

      if (response.isSuccess) {
        await _loadWalletBalance();

        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          await authProvider.refreshProfile();

          _showSuccessSnackBar('Funds added successfully! You can now complete your payment.');
        }
      } else {
        if (mounted) {
          _showErrorSnackBar(response.message ?? 'Failed to add funds');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error adding funds: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _confirmPayment() async {
    setState(() => _isLoading = true);

    try {
      final response = await _paymentService.processPayment(auctionId: widget.auction.id, paymentMethod: 'wallet');

      if (response.isSuccess) {
        // Update auth provider balance
        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          await authProvider.refreshProfile();

          // Switch to delivery view
          setState(() {
            _isPaymentCompleted = true;
            _countdownTimer?.cancel();
          });

          _loadDeliveryStatus();
          _startDeliveryUpdateTimer();
        }
      } else {
        if (mounted) {
          _showErrorSnackBar(response.message ?? 'Payment failed');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error processing payment: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showDeliveryInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: AppColors.primary),
            SizedBox(width: 8),
            Text('Delivery Information'),
          ],
        ),
        content: const Text(
          'Your fish will be delivered fresh and properly packaged. '
          'Estimated delivery time is 2-4 hours depending on your location. '
          'You will receive real-time updates as your order progresses.',
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
      ),
    );
  }

  void _contactSeller() {
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Contact seller feature coming soon!')));
  }

  void _trackDelivery() {
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Live tracking feature coming soon!')));
  }

  Future<void> _viewReceipt() async {
    setState(() => _isLoading = true);

    try {
      // Get payments to find the payment ID for this auction
      final response = await _paymentService.getPayments();

      if (response.isSuccess && response.data != null) {
        final payments = response.data as List<dynamic>;

        // Find payment for this auction
        final payment = payments.firstWhere((p) => p['auction']['id'] == widget.auction.id, orElse: () => null);

        if (payment != null) {
          final paymentId = payment['id'] as String;

          // Navigate to invoice screen
          if (mounted) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => InvoiceScreen(auction: widget.auction, paymentId: paymentId),
              ),
            );
          }
        } else {
          _showErrorSnackBar('Payment not found for this auction');
        }
      } else {
        _showErrorSnackBar(response.message ?? 'Failed to get payment information');
      }
    } catch (e) {
      _showErrorSnackBar('Error loading receipt: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.error));
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.success));
  }
}

class DeliveryStep {
  final String title;
  final String description;
  final IconData icon;

  DeliveryStep({required this.title, required this.description, required this.icon});
}
