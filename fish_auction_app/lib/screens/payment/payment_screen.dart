import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../models/auction_model.dart';
import '../../models/payment_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/payment_service.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import 'invoice_screen.dart';

class PaymentScreen extends StatefulWidget {
  final Auction auction;

  const PaymentScreen({super.key, required this.auction});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final PaymentService _paymentService = PaymentService();
  bool _isLoading = false;
  String _selectedPaymentMethod = 'wallet';
  double _walletBalance = 0.0;

  @override
  void initState() {
    super.initState();
    _loadWalletBalance();
  }

  Future<void> _loadWalletBalance() async {
    setState(() => _isLoading = true);

    try {
      final response = await _paymentService.getWalletBalance();
      if (response.isSuccess) {
        setState(() => _walletBalance = response.data!);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to load wallet balance');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Complete Payment'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Payment Summary Card
              _buildPaymentSummaryCard(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Payment Method Selection
              _buildPaymentMethodSelection(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Wallet Balance (if wallet selected)
              if (_selectedPaymentMethod == 'wallet') _buildWalletBalanceCard(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Payment Terms
              _buildPaymentTerms(),

              const SizedBox(height: AppConstants.largePadding),

              // Payment Button
              _buildPaymentButton(),

              const SizedBox(height: AppConstants.defaultPadding),

              // Security Notice
              _buildSecurityNotice(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentSummaryCard() {
    final platformFee = widget.auction.currentPrice * 0.05; // 5% platform fee
    final total = widget.auction.currentPrice;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.receipt_long, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Payment Summary', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Auction Details
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Row(
                children: [
                  // Auction Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: widget.auction.images.isNotEmpty
                          ? DecorationImage(image: NetworkImage(widget.auction.primaryImage), fit: BoxFit.cover)
                          : null,
                      color: widget.auction.images.isEmpty ? Colors.grey[300] : null,
                    ),
                    child: widget.auction.images.isEmpty ? const Icon(Icons.image, color: Colors.grey) : null,
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.auction.title,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${widget.auction.fishType} • ${widget.auction.formattedQuantity}',
                          style: TextStyle(color: Colors.grey[600], fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Price Breakdown
            _buildPriceRow('Winning Bid', widget.auction.currentPrice),
            _buildPriceRow('Platform Fee (5%)', platformFee),
            const Divider(),
            _buildPriceRow('Total Amount', total, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontWeight: isTotal ? FontWeight.bold : FontWeight.normal, fontSize: isTotal ? 16 : 14),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.payment, color: AppColors.primary),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Payment Method', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Wallet Option
            _buildPaymentMethodOption(
              'wallet',
              'Wallet Balance',
              Icons.account_balance_wallet,
              'Pay using your wallet balance',
            ),

            const SizedBox(height: AppConstants.smallPadding),

            // Credit Card Option
            _buildPaymentMethodOption(
              'stripe',
              'Credit/Debit Card',
              Icons.credit_card,
              'Pay using your credit or debit card',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodOption(String value, String title, IconData icon, String subtitle) {
    final isSelected = _selectedPaymentMethod == value;

    return GestureDetector(
      onTap: () => setState(() => _selectedPaymentMethod = value),
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          border: Border.all(color: isSelected ? AppColors.primary : Colors.grey[300]!, width: isSelected ? 2 : 1),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          color: isSelected ? AppColors.primary.withOpacity(0.05) : null,
        ),
        child: Row(
          children: [
            Icon(icon, color: isSelected ? AppColors.primary : Colors.grey[600]),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(fontWeight: FontWeight.bold, color: isSelected ? AppColors.primary : null),
                  ),
                  Text(subtitle, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                ],
              ),
            ),
            Radio<String>(
              value: value,
              groupValue: _selectedPaymentMethod,
              onChanged: (value) => setState(() => _selectedPaymentMethod = value!),
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletBalanceCard() {
    final hasInsufficientFunds = _walletBalance < widget.auction.currentPrice;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: hasInsufficientFunds ? AppColors.error : AppColors.success),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Wallet Balance', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Available Balance:', style: TextStyle(color: Colors.grey[600])),
                Text(
                  '\$${_walletBalance.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: hasInsufficientFunds ? AppColors.error : AppColors.success,
                  ),
                ),
              ],
            ),

            if (hasInsufficientFunds) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(color: AppColors.error),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: AppColors.error),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Insufficient Balance',
                            style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.error),
                          ),
                          Text(
                            'You need \$${(widget.auction.currentPrice - _walletBalance).toStringAsFixed(2)} more to complete this payment.',
                            style: const TextStyle(fontSize: 12, color: AppColors.error),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _navigateToWalletTopUp(),
                  icon: const Icon(Icons.add),
                  label: const Text('Top Up Wallet'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentTerms() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppColors.info),
                const SizedBox(width: AppConstants.smallPadding),
                const Text('Payment Terms', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            _buildTermItem('• Payment must be completed within 20 minutes of winning'),
            _buildTermItem('• Funds will be transferred to seller after successful payment'),
            _buildTermItem('• Platform fee (5%) is non-refundable'),
            _buildTermItem('• Refunds are subject to our refund policy'),
          ],
        ),
      ),
    );
  }

  Widget _buildTermItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(text, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
    );
  }

  Widget _buildPaymentButton() {
    final canPay =
        _selectedPaymentMethod == 'stripe' ||
        (_selectedPaymentMethod == 'wallet' && _walletBalance >= widget.auction.currentPrice);

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: canPay ? _processPayment : null,
        icon: const Icon(Icons.payment),
        label: Text(
          'Pay \$${widget.auction.currentPrice.toStringAsFixed(2)}',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.success,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          disabledBackgroundColor: Colors.grey[300],
        ),
      ),
    );
  }

  Widget _buildSecurityNotice() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.info.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppColors.info),
      ),
      child: Row(
        children: [
          const Icon(Icons.security, color: AppColors.info),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Secure Payment',
                  style: TextStyle(fontWeight: FontWeight.bold, color: AppColors.info),
                ),
                Text(
                  'Your payment information is encrypted and secure. We use industry-standard security measures.',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayment() async {
    setState(() => _isLoading = true);

    try {
      if (_selectedPaymentMethod == 'stripe') {
        // For Stripe payments, process directly with Stripe
        if (mounted) {
          final stripeResponse = await _paymentService.topUpWalletWithStripe(
            amount: widget.auction.currentPrice,
            context: context,
          );

          if (stripeResponse.isSuccess) {
            // After successful Stripe payment, process auction payment with wallet
            final confirmResponse = await _paymentService.processPayment(
              auctionId: widget.auction.id,
              paymentMethod: 'wallet', // Use wallet after Stripe top-up
            );

            if (confirmResponse.isSuccess) {
              _showSuccessDialog();
            } else {
              if (mounted) {
                _showErrorSnackBar(confirmResponse.message ?? 'Payment confirmation failed');
              }
            }
          } else {
            if (mounted) {
              _showErrorSnackBar(stripeResponse.message ?? 'Stripe payment failed');
            }
          }
        }
      } else {
        // For wallet payments
        final response = await _paymentService.processPayment(
          auctionId: widget.auction.id,
          paymentMethod: _selectedPaymentMethod,
        );

        if (response.isSuccess) {
          _showSuccessDialog();
        } else {
          if (mounted) {
            _showErrorSnackBar(response.message ?? 'Payment failed');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Payment processing failed: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: AppColors.success),
            const SizedBox(width: AppConstants.smallPadding),
            const Text('Payment Successful!'),
          ],
        ),
        content: Text(
          'Your payment for "${widget.auction.title}" has been processed successfully. You can now track your delivery.',
        ),
        actions: [
          OutlinedButton.icon(
            onPressed: () async {
              Navigator.of(context).pop(); // Close dialog
              await _navigateToReceipt();
            },
            icon: const Icon(Icons.receipt),
            label: const Text('View Receipt'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous screen
              // Navigate back to auctions - delivery tracking is now integrated
              Navigator.of(context).pushReplacementNamed('/main');
            },
            child: const Text('Track Delivery'),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToReceipt() async {
    try {
      // Get payments to find the payment ID for this auction
      final response = await _paymentService.getPayments();

      if (response.isSuccess && response.data != null) {
        final payments = response.data as List<dynamic>;

        // Find payment for this auction
        final payment = payments.firstWhere((p) => p['auction']['id'] == widget.auction.id, orElse: () => null);

        if (payment != null) {
          final paymentId = payment['id'] as String;

          // Navigate to invoice screen
          if (mounted) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => InvoiceScreen(auction: widget.auction, paymentId: paymentId),
              ),
            );
          }
        } else {
          _showErrorSnackBar('Payment not found for this auction');
        }
      } else {
        _showErrorSnackBar(response.message ?? 'Failed to get payment information');
      }
    } catch (e) {
      _showErrorSnackBar('Error loading receipt: ${e.toString()}');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.error));
  }

  void _navigateToWalletTopUp() {
    _showTopUpDialog();
  }

  void _showTopUpDialog() {
    final requiredAmount = widget.auction.currentPrice - _walletBalance;
    final suggestedAmount = (requiredAmount + 10).ceilToDouble(); // Add $10 buffer
    final amountController = TextEditingController(text: suggestedAmount.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.add_card, color: AppColors.primary),
            const SizedBox(width: AppConstants.smallPadding),
            const Text('Top Up Wallet'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You need \$${requiredAmount.toStringAsFixed(2)} more to complete this payment.',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Amount to add',
                prefixText: '\$',
                hintText: '0.00',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(AppConstants.borderRadius)),
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Minimum: \$${requiredAmount.toStringAsFixed(2)}',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processWalletTopUp(amountController.text);
            },
            child: const Text('Top Up'),
          ),
        ],
      ),
    );
  }

  Future<void> _processWalletTopUp(String amount) async {
    final double? amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue <= 0) {
      _showErrorSnackBar('Please enter a valid amount');
      return;
    }

    final requiredAmount = widget.auction.currentPrice - _walletBalance;
    if (amountValue < requiredAmount) {
      _showErrorSnackBar('Amount must be at least \$${requiredAmount.toStringAsFixed(2)}');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await _paymentService.topUpWalletWithStripe(amount: amountValue, context: context);

      if (response.isSuccess) {
        // Refresh wallet balance
        await _loadWalletBalance();

        // Update auth provider
        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          await authProvider.refreshProfile();

          _showSuccessSnackBar('Wallet topped up successfully! You can now complete your payment.');
        }
      } else {
        if (mounted) {
          _showErrorSnackBar(response.message ?? 'Top-up failed');
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error processing top-up: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: AppColors.success));
  }
}
