import 'package:flutter/material.dart';
import '../models/payment_model.dart';
import '../utils/api_response.dart';
import 'api_service.dart';
import 'stripe_service.dart';
import '../constants/app_constants.dart';

class PaymentService {
  final ApiService _apiService = ApiService();
  final StripeService _stripeService = StripeService();

  // Get wallet balance
  Future<ApiResponse<double>> getWalletBalance() async {
    try {
      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/wallet/balance/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final balance = (data['balance'] as num).toDouble();

        return ApiResponse.success(data: balance, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch wallet balance',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching wallet balance: ${e.toString()}', statusCode: 500);
    }
  }

  // Get wallet transactions
  Future<ApiResponse<List<WalletTransaction>>> getWalletTransactions({int? limit, int? offset}) async {
    try {
      final queryParams = <String, String>{};
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final response = await _apiService.get(
        '${AppConstants.paymentsEndpoint}/wallet/transactions/',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List<dynamic>;

        final transactions = results.map((json) => WalletTransaction.fromJson(json as Map<String, dynamic>)).toList();

        return ApiResponse.success(data: transactions, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch wallet transactions',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching wallet transactions: ${e.toString()}', statusCode: 500);
    }
  }

  // Top up wallet
  Future<ApiResponse<Map<String, dynamic>>> topUpWallet({required double amount, String? paymentMethodId}) async {
    try {
      final data = <String, dynamic>{'amount': amount};

      if (paymentMethodId != null) {
        data['payment_method_id'] = paymentMethodId;
      }

      final response = await _apiService.post('${AppConstants.paymentsEndpoint}/wallet/topup/', data: data);

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to top up wallet',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error topping up wallet: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user payments (raw data for invoice purposes)
  Future<ApiResponse<dynamic>> getPayments({int? limit, int? offset}) async {
    try {
      final queryParams = <String, String>{};
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/', queryParameters: queryParams);

      print('🔍 Raw API response success: ${response.isSuccess}');
      print('🔍 Raw API response data: ${response.data}');
      print('🔍 Raw API response data type: ${response.data.runtimeType}');

      if (response.isSuccess) {
        // Return the raw data as-is for invoice processing
        return ApiResponse.success(data: response.data, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch payments',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('🔍 Exception in getPayments: $e');
      return ApiResponse.error(message: 'Error fetching payments: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user payments as Payment objects
  Future<ApiResponse<List<Payment>>> getPaymentObjects({int? limit, int? offset}) async {
    try {
      final queryParams = <String, String>{};
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/', queryParameters: queryParams);

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final results = data['results'] as List<dynamic>;

        final payments = results.map((json) => Payment.fromJson(json as Map<String, dynamic>)).toList();

        return ApiResponse.success(data: payments, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch payments',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching payments: ${e.toString()}', statusCode: 500);
    }
  }

  // Get payment details
  Future<ApiResponse<Payment>> getPaymentDetails(String paymentId) async {
    try {
      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/$paymentId/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final payment = Payment.fromJson(data);

        return ApiResponse.success(data: payment, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch payment details',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching payment details: ${e.toString()}', statusCode: 500);
    }
  }

  // Process payment for auction
  Future<ApiResponse<Map<String, dynamic>>> processPayment({
    required int auctionId,
    required String paymentMethod,
    String? paymentMethodId,
  }) async {
    try {
      final data = <String, dynamic>{'payment_method': paymentMethod};

      if (paymentMethodId != null) {
        data['payment_method_id'] = paymentMethodId;
      }

      final response = await _apiService.post('${AppConstants.paymentsEndpoint}/process/$auctionId/', data: data);

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to process payment',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error processing payment: ${e.toString()}', statusCode: 500);
    }
  }

  // Create payment intent for wallet top-up
  Future<ApiResponse<PaymentIntent>> createWalletTopUpIntent(double amount) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/wallet/create-intent/',
        data: {'amount': amount},
      );

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        final paymentIntent = PaymentIntent.fromJson(data);

        return ApiResponse.success(data: paymentIntent, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to create payment intent',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error creating payment intent: ${e.toString()}', statusCode: 500);
    }
  }

  // Confirm payment intent
  Future<ApiResponse<Map<String, dynamic>>> confirmPaymentIntent({
    required String paymentIntentId,
    String? paymentMethodId,
  }) async {
    try {
      final data = <String, dynamic>{'payment_intent_id': paymentIntentId};

      if (paymentMethodId != null) {
        data['payment_method_id'] = paymentMethodId;
      }

      final response = await _apiService.post('${AppConstants.paymentsEndpoint}/confirm-intent/', data: data);

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to confirm payment',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error confirming payment: ${e.toString()}', statusCode: 500);
    }
  }

  // Request refund
  Future<ApiResponse<Map<String, dynamic>>> requestRefund({
    required String paymentId,
    required double amount,
    required String reason,
    String? description,
  }) async {
    try {
      final data = <String, dynamic>{'amount': amount, 'reason': reason};

      if (description != null) {
        data['description'] = description;
      }

      final response = await _apiService.post('${AppConstants.paymentsEndpoint}/$paymentId/refund/', data: data);

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to request refund',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error requesting refund: ${e.toString()}', statusCode: 500);
    }
  }

  // Stripe Integration Methods

  // Initialize Stripe
  Future<void> initializeStripe() async {
    await _stripeService.initialize();
  }

  // Top up wallet using Stripe
  Future<ApiResponse<Map<String, dynamic>>> topUpWalletWithStripe({
    required double amount,
    required BuildContext context,
  }) async {
    try {
      // Ensure Stripe is initialized
      if (!_stripeService.isInitialized) {
        await _stripeService.initialize();
      }

      // Process payment through Stripe
      return await _stripeService.processWalletTopUp(amount: amount, context: context);
    } catch (e) {
      return ApiResponse.error(message: 'Error processing Stripe payment: ${e.toString()}', statusCode: 500);
    }
  }

  // Withdraw funds from wallet
  Future<ApiResponse<Map<String, dynamic>>> withdrawFromWallet({required double amount}) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/wallet/withdraw/',
        data: {'amount': amount},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to withdraw from wallet',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error withdrawing from wallet: ${e.toString()}', statusCode: 500);
    }
  }

  // Create payout for seller
  Future<ApiResponse<Map<String, dynamic>>> createSellerPayout({required double amount}) async {
    try {
      // Get seller's connected account status
      final accountResponse = await _stripeService.getConnectedAccountStatus();
      if (!accountResponse.isSuccess) {
        return accountResponse;
      }

      final accountData = accountResponse.data!;
      final accountId = accountData['account_id'] as String?;

      if (accountId == null) {
        return ApiResponse.error(
          message: 'No connected account found. Please complete account setup.',
          statusCode: 400,
        );
      }

      // Create payout
      return await _stripeService.createPayout(amount: amount, accountId: accountId);
    } catch (e) {
      return ApiResponse.error(message: 'Error creating payout: ${e.toString()}', statusCode: 500);
    }
  }

  // Get invoice for payment
  Future<ApiResponse<Map<String, dynamic>>> getInvoice(String paymentId, {String language = 'en'}) async {
    try {
      final queryParams = {'language': language};
      final response = await _apiService.get(
        '${AppConstants.paymentsEndpoint}/$paymentId/invoice/',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(message: response.message ?? 'Failed to get invoice', statusCode: response.statusCode);
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error getting invoice: ${e.toString()}', statusCode: 500);
    }
  }

  // Download invoice PDF
  Future<ApiResponse<String>> downloadInvoicePDF(String paymentId) async {
    try {
      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/$paymentId/invoice/download/');

      if (response.isSuccess) {
        final data = response.data as Map<String, dynamic>;
        return ApiResponse.success(data: data['pdf_url'] as String, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to download invoice',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error downloading invoice: ${e.toString()}', statusCode: 500);
    }
  }

  // Payout Methods
  Future<ApiResponse<List<Map<String, dynamic>>>> getPayoutMethods() async {
    try {
      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/payout-methods/');

      print('🔍 PaymentService.getPayoutMethods() called');
      print('📡 Raw API response success: ${response.isSuccess}');
      print('📊 Raw API response data: ${response.data}');
      print('📊 Raw API response data type: ${response.data.runtimeType}');

      if (response.isSuccess) {
        // Handle paginated response
        final Map<String, dynamic> responseData = response.data as Map<String, dynamic>;
        final List<dynamic> results = responseData['results'] as List<dynamic>;
        final List<Map<String, dynamic>> methods = results.map((item) => item as Map<String, dynamic>).toList();

        print('✅ Parsed ${methods.length} payment methods from paginated response');
        print('📊 Total count from API: ${responseData['count']}');
        for (int i = 0; i < methods.length; i++) {
          print(
            '   Method $i: ${methods[i]['payout_type']} - ${methods[i]['paypal_email'] ?? methods[i]['payoneer_email'] ?? 'N/A'}',
          );
        }
        return ApiResponse.success(data: methods, statusCode: response.statusCode);
      } else {
        print('❌ API response failed: ${response.message}');
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch payout methods',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('❌ Exception in getPayoutMethods: $e');
      return ApiResponse.error(message: 'Error fetching payout methods: ${e.toString()}', statusCode: 500);
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> addPayoutMethod(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.post('${AppConstants.paymentsEndpoint}/payout-methods/', data: data);

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to add payout method',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error adding payout method: ${e.toString()}', statusCode: 500);
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> setDefaultPayoutMethod(String methodId) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/payout-methods/$methodId/set-default/',
        data: {},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to set default payout method',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error setting default payout method: ${e.toString()}', statusCode: 500);
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> testPayoutMethod(String methodId) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/payout-methods/$methodId/test/',
        data: {},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to test payout method',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error testing payout method: ${e.toString()}', statusCode: 500);
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> deletePayoutMethod(String methodId) async {
    try {
      final response = await _apiService.delete('${AppConstants.paymentsEndpoint}/payout-methods/$methodId/');

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to delete payout method',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error deleting payout method: ${e.toString()}', statusCode: 500);
    }
  }

  // Check if user has payout methods
  Future<ApiResponse<Map<String, dynamic>>> checkPayoutMethods() async {
    try {
      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/payout-methods/check/');

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to check payout methods',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error checking payout methods: ${e.toString()}', statusCode: 500);
    }
  }

  // Request withdrawal
  Future<ApiResponse<Map<String, dynamic>>> requestWithdrawal({
    required double amount,
    required String payoutMethodId,
  }) async {
    try {
      final response = await _apiService.post(
        '${AppConstants.paymentsEndpoint}/withdrawals/request/',
        data: {'amount': amount, 'payout_method_id': payoutMethodId},
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to request withdrawal',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error requesting withdrawal: ${e.toString()}', statusCode: 500);
    }
  }

  // Get user's withdrawal requests
  Future<ApiResponse<Map<String, dynamic>>> getUserWithdrawalRequests() async {
    try {
      final response = await _apiService.get('${AppConstants.paymentsEndpoint}/withdrawals/my-requests/');

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data as Map<String, dynamic>, statusCode: response.statusCode);
      } else {
        return ApiResponse.error(
          message: response.message ?? 'Failed to fetch withdrawal requests',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error fetching withdrawal requests: ${e.toString()}', statusCode: 500);
    }
  }

  // Get seller onboarding link
  Future<ApiResponse<String>> getSellerOnboardingLink() async {
    return await _stripeService.getAccountOnboardingLink();
  }

  // Get connected account status
  Future<ApiResponse<Map<String, dynamic>>> getConnectedAccountStatus() async {
    return await _stripeService.getConnectedAccountStatus();
  }

  // Check if user can receive payouts
  Future<bool> canReceivePayouts() async {
    try {
      final response = await getConnectedAccountStatus();
      if (response.isSuccess) {
        final data = response.data!;
        return data['charges_enabled'] == true && data['payouts_enabled'] == true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
