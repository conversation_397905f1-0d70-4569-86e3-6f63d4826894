import 'user_model.dart';

class AuctionCategory {
  final int id;
  final String name;
  final String nameAr;
  final String? description;
  final String? descriptionAr;
  final String? icon;
  final bool isActive;

  AuctionCategory({
    required this.id,
    required this.name,
    required this.nameAr,
    this.description,
    this.descriptionAr,
    this.icon,
    required this.isActive,
  });

  factory AuctionCategory.fromJson(Map<String, dynamic> json) {
    return AuctionCategory(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? json['name'] ?? '',
      description: json['description'],
      descriptionAr: json['description_ar'] ?? json['description'],
      icon: json['icon'],
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'description': description,
      'description_ar': descriptionAr,
      'icon': icon,
      'is_active': isActive,
    };
  }

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? nameAr : name;
  }

  String? getLocalizedDescription(String languageCode) {
    return languageCode == 'ar' ? descriptionAr : description;
  }
}

class Auction {
  final int id;
  final String title;
  final String titleAr;
  final String description;
  final String descriptionAr;
  final User seller;
  final AuctionCategory category;
  final double startingPrice;
  final double currentPrice;
  final double? reservePrice;
  final double? targetPrice;
  final double bidIncrement;
  final String status;
  final String auctionType;
  final DateTime startTime;
  final DateTime? endTime;
  final int totalBids;
  final List<String> images;
  final String? location;
  final double? latitude;
  final double? longitude;
  final DateTime? locationUpdatedAt;
  final bool isLocationLive;
  final String? deliveryAddress;
  final String fishType;
  final double quantity;
  final String unit;
  final String qualityGrade;
  final String? specialNotes;
  final bool isWatched;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User? winner;
  final DateTime? paymentDeadline;
  final bool paymentReceived;

  Auction({
    required this.id,
    required this.title,
    required this.titleAr,
    required this.description,
    required this.descriptionAr,
    required this.seller,
    required this.category,
    required this.startingPrice,
    required this.currentPrice,
    this.reservePrice,
    this.targetPrice,
    required this.bidIncrement,
    required this.status,
    required this.auctionType,
    required this.startTime,
    this.endTime,
    required this.totalBids,
    required this.images,
    this.location,
    this.latitude,
    this.longitude,
    this.locationUpdatedAt,
    required this.isLocationLive,
    this.deliveryAddress,
    required this.fishType,
    required this.quantity,
    required this.unit,
    required this.qualityGrade,
    this.specialNotes,
    required this.isWatched,
    required this.createdAt,
    required this.updatedAt,
    this.winner,
    this.paymentDeadline,
    required this.paymentReceived,
  });

  factory Auction.fromJson(Map<String, dynamic> json) {
    // Handle both API response format and local format
    dynamic categoryData = json['category'] ?? json['fish_category'] ?? {};

    // Handle category - API might return int ID or Map object
    AuctionCategory category;
    if (categoryData is int) {
      // If API returns just the category ID, create a minimal category object
      category = AuctionCategory(
        id: categoryData,
        name: 'Category $categoryData',
        nameAr: 'فئة $categoryData',
        isActive: true,
      );
    } else if (categoryData is Map<String, dynamic>) {
      category = AuctionCategory.fromJson(categoryData);
    } else {
      // Fallback category
      category = AuctionCategory(id: 0, name: 'Unknown Category', nameAr: 'فئة غير معروفة', isActive: true);
    }

    // Handle seller - API might return int ID or Map object
    User seller;
    dynamic sellerData = json['seller'] ?? {};
    if (sellerData is int) {
      // If API returns just the seller ID, create a minimal seller object
      seller = User(
        id: sellerData,
        username: 'User $sellerData',
        email: 'user$<EMAIL>',
        firstName: 'User',
        lastName: '$sellerData',
        userType: 'seller',
        isVerified: false,
        isActive: true,
        kycStatus: 'not_submitted',
        walletBalance: 0.0,
        preferredLanguage: 'en',
        dateJoined: DateTime.now(),
      );
    } else if (sellerData is Map<String, dynamic>) {
      seller = User.fromJson(sellerData);
    } else {
      // Fallback seller
      seller = User(
        id: 0,
        username: 'Unknown User',
        email: '<EMAIL>',
        firstName: 'Unknown',
        lastName: 'User',
        userType: 'seller',
        isVerified: false,
        isActive: true,
        kycStatus: 'not_submitted',
        walletBalance: 0.0,
        preferredLanguage: 'en',
        dateJoined: DateTime.now(),
      );
    }

    // Handle images - API returns main_image, we need a list
    List<String> imagesList = [];

    // First check if images array has content
    if (json['images'] != null && json['images'] is List && (json['images'] as List).isNotEmpty) {
      imagesList = List<String>.from(json['images']);
    }
    // If images array is empty or null, use main_image
    else if (json['main_image'] != null && json['main_image'].toString().isNotEmpty) {
      final mainImageUrl = json['main_image'].toString();
      imagesList = [mainImageUrl];
    }

    // Handle dates - API might not include created_at/updated_at
    DateTime createdAt;
    DateTime updatedAt;

    try {
      createdAt = DateTime.parse(json['created_at'] ?? json['start_time']);
    } catch (e) {
      createdAt = DateTime.now();
    }

    try {
      updatedAt = DateTime.parse(json['updated_at'] ?? json['start_time']);
    } catch (e) {
      updatedAt = DateTime.now();
    }

    return Auction(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch,
      title: json['title'] ?? '',
      titleAr: json['title_ar'] ?? json['title'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? json['description'] ?? '',
      seller: seller,
      category: category,
      startingPrice: _parseDouble(json['starting_price']),
      currentPrice: _parseDouble(json['current_price'] ?? json['starting_price']),
      reservePrice: _parseDoubleNullable(json['reserve_price']),
      targetPrice: _parseDoubleNullable(json['target_price']),
      bidIncrement: _parseDouble(json['bid_increment']) == 0.0 ? 1.0 : _parseDouble(json['bid_increment']),
      status: json['status'] ?? 'draft',
      auctionType: json['auction_type'] ?? 'live',
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null ? DateTime.parse(json['end_time']) : null,
      totalBids: json['total_bids'] ?? 0,
      images: imagesList,
      location: json['location'] ?? json['catch_location'],
      latitude: _parseDoubleNullable(json['latitude']),
      longitude: _parseDoubleNullable(json['longitude']),
      locationUpdatedAt: json['location_updated_at'] != null ? DateTime.parse(json['location_updated_at']) : null,
      isLocationLive: json['is_location_live'] ?? false,
      deliveryAddress: json['delivery_address'],
      fishType: json['fish_type'] ?? '',
      quantity: _parseDouble(json['quantity'] ?? json['weight']),
      unit: json['unit'] ?? 'kg',
      qualityGrade: json['quality_grade'] ?? 'A',
      specialNotes: json['special_notes'],
      isWatched: json['is_watched'] ?? false,
      createdAt: createdAt,
      updatedAt: updatedAt,
      winner: _parseWinner(json['winner']),
      paymentDeadline: json['payment_deadline'] != null ? DateTime.parse(json['payment_deadline']) : null,
      paymentReceived: json['payment_received'] ?? false,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  static double? _parseDoubleNullable(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      if (value.isEmpty) return null;
      final parsed = double.tryParse(value);
      return parsed;
    }
    // Handle Decimal/num types
    if (value is num) return value.toDouble();
    return null;
  }

  static User? _parseWinner(dynamic winnerData) {
    if (winnerData == null) return null;

    if (winnerData is int) {
      // If API returns just the winner ID, create a minimal winner object
      return User(
        id: winnerData,
        username: 'User $winnerData',
        email: 'user$<EMAIL>',
        firstName: 'User',
        lastName: '$winnerData',
        userType: 'buyer',
        isVerified: false,
        isActive: true,
        kycStatus: 'not_submitted',
        walletBalance: 0.0,
        preferredLanguage: 'en',
        dateJoined: DateTime.now(),
      );
    } else if (winnerData is Map<String, dynamic>) {
      try {
        return User.fromJson(winnerData);
      } catch (e) {
        print('Error parsing winner: $e');
        return null;
      }
    }

    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'title_ar': titleAr,
      'description': description,
      'description_ar': descriptionAr,
      'seller': seller.toJson(),
      'category': category.toJson(),
      'starting_price': startingPrice,
      'current_price': currentPrice,
      'reserve_price': reservePrice,
      'target_price': targetPrice,
      'bid_increment': bidIncrement,
      'status': status,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'total_bids': totalBids,
      'images': images,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'location_updated_at': locationUpdatedAt?.toIso8601String(),
      'is_location_live': isLocationLive,
      'delivery_address': deliveryAddress,
      'fish_type': fishType,
      'quantity': quantity,
      'unit': unit,
      'quality_grade': qualityGrade,
      'special_notes': specialNotes,
      'is_watched': isWatched,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'winner': winner?.toJson(),
      'payment_deadline': paymentDeadline?.toIso8601String(),
      'payment_received': paymentReceived,
    };
  }

  String getLocalizedTitle(String languageCode) {
    return languageCode == 'ar' ? titleAr : title;
  }

  String getLocalizedDescription(String languageCode) {
    return languageCode == 'ar' ? descriptionAr : description;
  }

  String get statusDisplay {
    // Show "Direct" for buy_now auctions instead of "Live"
    if (auctionType == 'buy_now' && status == 'live') {
      return 'Direct';
    }

    switch (status) {
      case 'draft':
        return 'Draft';
      case 'scheduled':
        return 'Scheduled';
      case 'live':
        return 'Live';
      case 'ended':
        return 'Ended';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  bool get isLive => status == 'live';
  bool get isEnded => status == 'ended';
  bool get isScheduled => status == 'scheduled';
  bool get isDraft => status == 'draft';
  bool get isCancelled => status == 'cancelled';

  Duration get timeRemaining {
    final now = DateTime.now();
    if (isLive && endTime != null) {
      final remaining = endTime!.difference(now);
      // Return zero if auction has expired
      return remaining.isNegative ? Duration.zero : remaining;
    } else if (isScheduled) {
      final remaining = startTime.difference(now);
      // Return zero if start time has passed
      return remaining.isNegative ? Duration.zero : remaining;
    }
    return Duration.zero;
  }

  bool get hasTimeRemaining => timeRemaining.inSeconds > 0;

  String get primaryImage {
    return images.isNotEmpty ? images.first : '';
  }

  bool get hasLiveLocation => isLocationLive && latitude != null && longitude != null;

  double get nextBidAmount => currentPrice + bidIncrement;

  bool get hasReservePrice => reservePrice != null && reservePrice! > 0;

  bool get reserveMet => hasReservePrice ? currentPrice >= reservePrice! : true;

  String get formattedQuantity => '$quantity $unit';

  String get sellerName => seller.displayName;

  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  // Auto-bidding and participation properties (would come from API)
  bool get hasAutoBid => false; // TODO: Implement based on API response
  bool get hasManualBids => false; // TODO: Implement based on API response
  bool get isWon => winner != null; // Check if auction has a winner
  bool get isAutoBidActive => false; // TODO: Implement based on API response
  int get myBidsCount => 0; // TODO: Implement based on API response
  double? get myHighestBid => null; // TODO: Implement based on API response
  double? get autoBidMaxAmount => null; // TODO: Implement based on API response
  double? get autoBidRemainingAmount => null; // TODO: Implement based on API response

  // Payment-related getters
  bool get payment_received => paymentReceived;
  DateTime? get payment_deadline => paymentDeadline;

  Auction copyWith({
    int? id,
    String? title,
    String? titleAr,
    String? description,
    String? descriptionAr,
    User? seller,
    AuctionCategory? category,
    double? startingPrice,
    double? currentPrice,
    double? reservePrice,
    double? targetPrice,
    double? bidIncrement,
    String? status,
    String? auctionType,
    DateTime? startTime,
    DateTime? endTime,
    int? totalBids,
    List<String>? images,
    String? location,
    double? latitude,
    double? longitude,
    DateTime? locationUpdatedAt,
    bool? isLocationLive,
    String? deliveryAddress,
    String? fishType,
    double? quantity,
    String? unit,
    String? qualityGrade,
    String? specialNotes,
    bool? isWatched,
    DateTime? createdAt,
    DateTime? updatedAt,
    User? winner,
    DateTime? paymentDeadline,
    bool? paymentReceived,
  }) {
    return Auction(
      id: id ?? this.id,
      title: title ?? this.title,
      titleAr: titleAr ?? this.titleAr,
      description: description ?? this.description,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      seller: seller ?? this.seller,
      category: category ?? this.category,
      startingPrice: startingPrice ?? this.startingPrice,
      currentPrice: currentPrice ?? this.currentPrice,
      reservePrice: reservePrice ?? this.reservePrice,
      targetPrice: targetPrice ?? this.targetPrice,
      bidIncrement: bidIncrement ?? this.bidIncrement,
      status: status ?? this.status,
      auctionType: auctionType ?? this.auctionType,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalBids: totalBids ?? this.totalBids,
      images: images ?? this.images,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationUpdatedAt: locationUpdatedAt ?? this.locationUpdatedAt,
      isLocationLive: isLocationLive ?? this.isLocationLive,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      fishType: fishType ?? this.fishType,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      qualityGrade: qualityGrade ?? this.qualityGrade,
      specialNotes: specialNotes ?? this.specialNotes,
      isWatched: isWatched ?? this.isWatched,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      winner: winner ?? this.winner,
      paymentDeadline: paymentDeadline ?? this.paymentDeadline,
      paymentReceived: paymentReceived ?? this.paymentReceived,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Auction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Auction{id: $id, title: $title, status: $status, currentPrice: $currentPrice}';
  }
}
