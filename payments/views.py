from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db import models
from django.conf import settings
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from .models import Payment, WalletTransaction, Invoice, Refund, PayoutMethod, WithdrawalRequest
from .serializers import (
    PaymentSerializer, WalletTransactionSerializer, InvoiceSerializer,
    RefundSerializer, WalletTopUpSerializer, ProcessPaymentSerializer,
    RefundRequestSerializer
)
from .services import PaymentService, WalletService
from auctions.models import Auction
import logging

logger = logging.getLogger(__name__)


class WalletBalanceView(APIView):
    """Get user's wallet balance"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get wallet balance",
        description="Get the authenticated user's current wallet balance"
    )
    def get(self, request):
        wallet_service = WalletService()
        balance = wallet_service.get_wallet_balance(request.user)
        return Response({'balance': balance})


class WalletTransactionsView(generics.ListAPIView):
    """Get user's wallet transaction history"""

    serializer_class = WalletTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return WalletTransaction.objects.filter(user=self.request.user)

    @extend_schema(
        summary="Get wallet transactions",
        description="Get the authenticated user's wallet transaction history"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class WalletTopUpView(APIView):
    """Top up user's wallet"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Top up wallet",
        description="Create payment intent for wallet top-up",
        request=WalletTopUpSerializer
    )
    def post(self, request):
        serializer = WalletTopUpSerializer(data=request.data)
        if serializer.is_valid():
            try:
                amount = serializer.validated_data['amount']

                # Validate amount
                if amount <= 0:
                    return Response(
                        {'error': 'Amount must be greater than 0'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                if amount > 10000:  # Max $10,000 per transaction
                    return Response(
                        {'error': 'Maximum top-up amount is $10,000'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Just return success - frontend handles payment
                return Response({
                    'message': 'Ready for payment',
                    'amount': float(amount)
                })

            except Exception as e:
                logger.error(f"Wallet top-up failed for user {request.user.id}: {str(e)}")
                return Response(
                    {'error': 'Failed to process wallet top-up'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PaymentListView(generics.ListAPIView):
    """Get user's payments"""

    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        return Payment.objects.filter(
            models.Q(buyer=user) | models.Q(seller=user)
        ).order_by('-created_at')

    @extend_schema(
        summary="Get payments",
        description="Get payments where the user is either buyer or seller"
    )
    def get(self, request, *args, **kwargs):
        try:
            logger.info(f"PaymentListView: Getting payments for user {request.user.id}")
            queryset = self.get_queryset()
            logger.info(f"PaymentListView: Found {queryset.count()} payments")

            # Try to serialize the data
            serializer = self.get_serializer(queryset, many=True)
            logger.info(f"PaymentListView: Serialization successful")

            return Response(serializer.data)
        except Exception as e:
            logger.error(f"PaymentListView error: {str(e)}")
            import traceback
            logger.error(f"PaymentListView traceback: {traceback.format_exc()}")
            return Response(
                {'error': f'Failed to get payments: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PaymentDetailView(generics.RetrieveAPIView):
    """Get payment details"""

    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        return Payment.objects.filter(
            models.Q(buyer=user) | models.Q(seller=user)
        )

    @extend_schema(
        summary="Get payment details",
        description="Get detailed information about a specific payment"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ProcessPaymentView(APIView):
    """Process payment for an auction"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Process payment",
        description="Process payment for a won auction",
        request=ProcessPaymentSerializer
    )
    def post(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)

        # Handle direct buy auctions differently
        if auction.auction_type == 'buy_now':
            # For direct buy auctions, just set the buyer as winner (don't end auction yet)
            if auction.status == 'live' and not auction.winner:
                auction.winner = request.user
                auction.current_price = auction.starting_price  # Use the fixed price
                auction.save()
        else:
            # For regular auctions, verify user is the winner
            if auction.winner != request.user:
                return Response(
                    {'error': 'You are not the winner of this auction'},
                    status=status.HTTP_403_FORBIDDEN
                )

        # Check if payment already exists
        try:
            payment = Payment.objects.get(auction=auction)
            if payment.status == 'completed':
                return Response(
                    {'error': 'Payment already completed'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Payment.DoesNotExist:
            # Create payment
            payment_service = PaymentService()
            payment = payment_service.create_payment(auction, request.user)

        serializer = ProcessPaymentSerializer(data=request.data)
        if serializer.is_valid():
            try:
                payment_method = serializer.validated_data['payment_method']
                payment_service = PaymentService()

                if payment_method == 'wallet':
                    payment = payment_service.process_wallet_payment(payment)
                    return Response({
                        'message': 'Payment processed successfully',
                        'payment': PaymentSerializer(payment).data
                    })

                elif payment_method == 'stripe':
                    payment_method_id = serializer.validated_data['payment_method_id']
                    intent = payment_service.process_stripe_payment(payment, payment_method_id)

                    return Response({
                        'payment_intent': {
                            'id': intent.id,
                            'status': intent.status,
                            'client_secret': intent.client_secret if intent.status == 'requires_action' else None
                        },
                        'payment': PaymentSerializer(payment).data
                    })

            except ValueError as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                logger.error(f"Payment processing failed: {str(e)}")
                return Response(
                    {'error': 'Payment processing failed'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class WalletConfirmTopUpView(APIView):
    """Confirm wallet top-up after successful payment"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Confirm wallet top-up",
        description="Confirm wallet top-up after successful payment"
    )
    def post(self, request):
        try:
            payment_intent_id = request.data.get('payment_intent_id')
            amount = request.data.get('amount')
            status_param = request.data.get('status', 'completed')

            if not amount:
                return Response(
                    {'error': 'amount is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate amount
            try:
                amount = float(amount)
                if amount <= 0:
                    return Response(
                        {'error': 'Amount must be greater than 0'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except (ValueError, TypeError):
                return Response(
                    {'error': 'Invalid amount format'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Add funds to wallet (no Stripe verification needed)
            if status_param == 'completed':
                logger.info(f"Adding {amount} to wallet for user {request.user.id}")
                try:
                    payment_service = PaymentService()
                    logger.info(f"PaymentService created successfully")

                    payment_service.add_wallet_funds(
                        request.user,
                        amount,
                        f"Wallet top-up (Payment ID: {payment_intent_id or 'N/A'})"
                    )
                    logger.info(f"Funds added successfully")

                    # Refresh user from database to get updated balance
                    request.user.refresh_from_db()
                    logger.info(f"User refreshed, new balance: {request.user.wallet_balance}")

                    return Response({
                        'message': 'Wallet topped up successfully',
                        'new_balance': float(request.user.wallet_balance),
                        'amount': float(amount)
                    })
                except Exception as e:
                    logger.error(f"Error adding funds to wallet: {str(e)}")
                    raise
            else:
                return Response(
                    {'error': 'Payment not completed'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f"Wallet top-up confirmation failed: {str(e)}")
            return Response(
                {'error': 'Failed to confirm wallet top-up'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WalletWithdrawView(APIView):
    """Withdraw funds from user's wallet"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Withdraw from wallet",
        description="Withdraw funds from user's wallet"
    )
    def post(self, request):
        try:
            amount = request.data.get('amount')

            if not amount:
                return Response(
                    {'error': 'amount is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate amount
            try:
                from decimal import Decimal
                amount = float(amount)
                if amount <= 0:
                    return Response(
                        {'error': 'Amount must be greater than 0'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                if amount < 10.0:
                    return Response(
                        {'error': 'Minimum withdrawal amount is $10.00'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Convert to Decimal for database operations
                decimal_amount = Decimal(str(amount))
            except (ValueError, TypeError):
                return Response(
                    {'error': 'Invalid amount format'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if user has sufficient balance
            if request.user.wallet_balance < decimal_amount:
                return Response(
                    {'error': 'Insufficient wallet balance'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Remove funds from wallet
            request.user.wallet_balance -= decimal_amount
            request.user.save()

            # Create transaction record (negative amount for withdrawal)
            from .models import WalletTransaction
            WalletTransaction.objects.create(
                user=request.user,
                transaction_type='withdrawal',
                amount=-decimal_amount,  # Negative amount for withdrawal
                status='completed',
                payout_status='pending',  # Withdrawal needs admin approval
                description=f"Wallet withdrawal"
            )

            return Response({
                'message': 'Withdrawal successful',
                'new_balance': float(request.user.wallet_balance),
                'amount': float(amount)
            })

        except Exception as e:
            import traceback
            logger.error(f"Wallet withdrawal failed: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return Response(
                {'error': f'Failed to process withdrawal: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StripeConfigView(APIView):
    """Get Stripe configuration for frontend"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get Stripe configuration",
        description="Get Stripe publishable key for frontend initialization"
    )
    def get(self, request):
        return Response({
            'publishable_key': settings.STRIPE_PUBLISHABLE_KEY
        })


# Payout Methods Views
class PayoutMethodListCreateView(generics.ListCreateAPIView):
    """List and create payout methods"""

    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        from .serializers import PayoutMethodSerializer
        return PayoutMethodSerializer

    def get_queryset(self):
        return PayoutMethod.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        # If this is the first payout method, make it default
        if not PayoutMethod.objects.filter(user=self.request.user).exists():
            serializer.save(user=self.request.user, is_default=True)
        else:
            serializer.save(user=self.request.user)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def request_withdrawal(request):
    """Create a withdrawal request"""

    amount = request.data.get('amount')
    payout_method_id = request.data.get('payout_method_id')

    if not amount or not payout_method_id:
        return Response(
            {'error': 'Amount and payout method are required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        from decimal import Decimal
        amount = Decimal(str(amount))
    except:
        return Response(
            {'error': 'Invalid amount'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Check minimum withdrawal amount
    if amount < Decimal('10.00'):
        return Response(
            {'error': 'Minimum withdrawal amount is $10'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Check if user has sufficient balance
    if request.user.wallet_balance < amount:
        return Response(
            {'error': 'Insufficient wallet balance'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Get payout method
    try:
        payout_method = PayoutMethod.objects.get(id=payout_method_id, user=request.user)
    except PayoutMethod.DoesNotExist:
        return Response(
            {'error': 'Payout method not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Calculate fees
    platform_fee = Decimal('2.00')  # $2 withdrawal fee
    payout_amount = amount - platform_fee

    if payout_amount <= 0:
        return Response(
            {'error': 'Amount too small after fees'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Deduct amount from user's balance immediately
    user = request.user
    if user.wallet_balance < amount:
        return Response(
            {'error': 'Insufficient balance'},
            status=status.HTTP_400_BAD_REQUEST
        )

    user.wallet_balance -= amount
    user.save()

    # Create withdrawal request (money already deducted from balance above)
    withdrawal = WithdrawalRequest.objects.create(
        user=request.user,
        payout_method=payout_method,
        amount=amount,
        platform_fee=platform_fee,
        payout_amount=payout_amount,
        status='pending'  # Waiting for admin approval
    )

    # Create wallet transaction for the withdrawal request (pending admin approval)
    WalletTransaction.objects.create(
        user=user,
        transaction_type='withdrawal',
        amount=-amount,
        status='pending',
        payout_status='pending_approval',  # Waiting for admin approval
        description=f"Withdrawal request #{withdrawal.id} - {payout_method.get_payout_type_display()}",
        withdrawal_request_id=str(withdrawal.id)  # Link to withdrawal request
    )

    return Response({
        'message': 'Withdrawal request submitted successfully',
        'withdrawal_id': withdrawal.id,
        'amount': amount,
        'platform_fee': platform_fee,
        'payout_amount': payout_amount,
        'status': 'pending'
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def check_payout_methods(request):
    """Check if user has any payout methods configured"""

    payout_methods = PayoutMethod.objects.filter(user=request.user)

    return Response({
        'has_payout_methods': payout_methods.exists(),
        'payout_methods_count': payout_methods.count(),
        'available_types': [
            {'value': 'paypal', 'label': 'PayPal', 'test_supported': True},
            {'value': 'stripe', 'label': 'Stripe', 'test_supported': True},
            {'value': 'bank_transfer', 'label': 'Bank Transfer', 'test_supported': False},
            {'value': 'payoneer', 'label': 'Payoneer', 'test_supported': True},
            {'value': 'telr', 'label': 'Telr', 'test_supported': True},
        ]
    })


class PayoutMethodDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a payout method"""

    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        from .serializers import PayoutMethodSerializer
        return PayoutMethodSerializer

    def get_queryset(self):
        return PayoutMethod.objects.filter(user=self.request.user)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def set_default_payout_method(request, payout_method_id):
    """Set a payout method as default"""

    try:
        payout_method = PayoutMethod.objects.get(id=payout_method_id, user=request.user)
    except PayoutMethod.DoesNotExist:
        return Response(
            {'error': 'Payout method not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Remove default from all other methods
    PayoutMethod.objects.filter(user=request.user).update(is_default=False)

    # Set this one as default
    payout_method.is_default = True
    payout_method.save()

    return Response({'message': 'Default payout method updated successfully'})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def test_payout_method(request, payout_method_id):
    """Test a payout method with a small amount"""

    try:
        payout_method = PayoutMethod.objects.get(id=payout_method_id, user=request.user)
    except PayoutMethod.DoesNotExist:
        return Response(
            {'error': 'Payout method not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    # Test with $0.01 for supported methods
    from decimal import Decimal
    test_amount = Decimal('0.01')

    if payout_method.payout_type == 'paypal':
        # Test PayPal payout (mock implementation)
        result = _test_paypal_payout(payout_method.paypal_email, test_amount)
    elif payout_method.payout_type == 'stripe':
        # Test Stripe payout (mock implementation)
        result = _test_stripe_payout(payout_method.stripe_account_id, test_amount)
    elif payout_method.payout_type == 'payoneer':
        # Test Payoneer payout (mock implementation)
        result = _test_payoneer_payout(payout_method.payoneer_email, test_amount)
    else:
        return Response(
            {'error': 'Test not supported for this payout method'},
            status=status.HTTP_400_BAD_REQUEST
        )

    if result['success']:
        # Mark as verified
        payout_method.is_verified = True
        payout_method.save()

        return Response({
            'message': 'Test payout successful! Method verified.',
            'transaction_id': result.get('transaction_id')
        })
    else:
        return Response(
            {'error': f'Test payout failed: {result.get("error")}'},
            status=status.HTTP_400_BAD_REQUEST
        )


def _test_paypal_payout(email, amount):
    """Test PayPal payout (mock implementation)"""
    # In production, integrate with PayPal API
    from django.utils import timezone
    return {
        'success': True,
        'transaction_id': f'PAYPAL_TEST_{timezone.now().strftime("%Y%m%d%H%M%S")}'
    }


def _test_stripe_payout(account_id, amount):
    """Test Stripe payout (mock implementation)"""
    # In production, integrate with Stripe API
    from django.utils import timezone
    return {
        'success': True,
        'transaction_id': f'STRIPE_TEST_{timezone.now().strftime("%Y%m%d%H%M%S")}'
    }


def _test_payoneer_payout(email, amount):
    """Test Payoneer payout (mock implementation)"""
    # In production, integrate with Payoneer API
    from django.utils import timezone
    return {
        'success': True,
        'transaction_id': f'PAYONEER_TEST_{timezone.now().strftime("%Y%m%d%H%M%S")}'
    }


# Admin API Views
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_withdrawal_requests(request):
    """Get all withdrawal requests for admin"""

    # Check if user is admin/staff
    if not request.user.is_staff:
        return Response(
            {'error': 'Admin access required'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Get filter parameters
    status_filter = request.GET.get('status', '')
    payout_type_filter = request.GET.get('payout_type', '')
    search = request.GET.get('search', '')

    # Build queryset
    queryset = WithdrawalRequest.objects.select_related('user', 'payout_method').all()

    if status_filter:
        queryset = queryset.filter(status=status_filter)

    if payout_type_filter:
        queryset = queryset.filter(payout_method__payout_type=payout_type_filter)

    if search:
        queryset = queryset.filter(
            models.Q(user__username__icontains=search) |
            models.Q(user__email__icontains=search)
        )

    # Serialize data
    withdrawals = []
    for withdrawal in queryset.order_by('-created_at'):
        payout_details = ""
        if withdrawal.payout_method.payout_type == 'paypal':
            payout_details = withdrawal.payout_method.paypal_email
        elif withdrawal.payout_method.payout_type == 'stripe':
            payout_details = f"Account ID: {withdrawal.payout_method.stripe_account_id}"
        elif withdrawal.payout_method.payout_type == 'bank_transfer':
            payout_details = f"{withdrawal.payout_method.bank_name} - {withdrawal.payout_method.account_number}"
        elif withdrawal.payout_method.payout_type == 'payoneer':
            payout_details = withdrawal.payout_method.payoneer_email

        withdrawals.append({
            'id': str(withdrawal.id),
            'user': {
                'username': withdrawal.user.username,
                'email': withdrawal.user.email,
                'user_type': withdrawal.user.user_type,
                'wallet_balance': float(withdrawal.user.wallet_balance),
            },
            'amount': float(withdrawal.amount),
            'platform_fee': float(withdrawal.platform_fee),
            'payout_amount': float(withdrawal.payout_amount),
            'status': withdrawal.status,
            'payout_method': {
                'type': withdrawal.payout_method.payout_type,
                'details': payout_details,
            },
            'admin_notes': withdrawal.admin_notes,
            'external_transaction_id': withdrawal.external_transaction_id,
            'created_at': withdrawal.created_at.isoformat(),
            'approved_at': withdrawal.approved_at.isoformat() if withdrawal.approved_at else None,
            'completed_at': withdrawal.completed_at.isoformat() if withdrawal.completed_at else None,
        })

    return Response({
        'withdrawals': withdrawals,
        'total_count': len(withdrawals)
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def admin_process_withdrawal(request, withdrawal_id):
    """Process a withdrawal request (approve/reject)"""

    # Check if user is admin/staff
    if not request.user.is_staff:
        return Response(
            {'error': 'Admin access required'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        withdrawal = WithdrawalRequest.objects.get(id=withdrawal_id)
    except WithdrawalRequest.DoesNotExist:
        return Response(
            {'error': 'Withdrawal request not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    action = request.data.get('action')  # 'approved', 'rejected', 'processing'
    admin_notes = request.data.get('admin_notes', '')
    external_transaction_id = request.data.get('external_transaction_id', '')

    if action not in ['approved', 'rejected', 'processing']:
        return Response(
            {'error': 'Invalid action. Must be approved, rejected, or processing'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Update withdrawal request
    withdrawal.status = action
    withdrawal.admin_notes = admin_notes
    withdrawal.external_transaction_id = external_transaction_id
    withdrawal.processed_by = request.user

    if action == 'approved':
        withdrawal.status = 'approved'
        withdrawal.approved_at = timezone.now()

        # Update the existing withdrawal transaction status
        withdrawal_transaction = WalletTransaction.objects.filter(
            withdrawal_request_id=str(withdrawal.id),
            transaction_type='withdrawal',
            payout_status='pending_approval'
        ).first()

        if withdrawal_transaction:
            withdrawal_transaction.payout_status = 'approved'
            withdrawal_transaction.external_transaction_id = external_transaction_id
            withdrawal_transaction.description = f"Withdrawal approved - {withdrawal.payout_method.get_payout_type_display()}"

            # If external transaction ID is provided, mark as completed (money sent)
            if external_transaction_id:
                withdrawal_transaction.status = 'completed'
                withdrawal_transaction.payout_status = 'completed'
                withdrawal.status = 'completed'
                withdrawal.completed_at = timezone.now()
            else:
                withdrawal_transaction.status = 'approved'

            withdrawal_transaction.save()

    elif action == 'rejected':
        withdrawal.status = 'rejected'
        withdrawal.rejected_at = timezone.now()

        # Return money to user's balance (it was already deducted when request was created)
        user = withdrawal.user
        user.wallet_balance += withdrawal.amount
        user.save()

        # Update the existing withdrawal transaction to rejected
        withdrawal_transaction = WalletTransaction.objects.filter(
            withdrawal_request_id=str(withdrawal.id),
            transaction_type='withdrawal',
            payout_status='pending_approval'
        ).first()

        if withdrawal_transaction:
            withdrawal_transaction.status = 'rejected'
            withdrawal_transaction.payout_status = 'rejected'
            withdrawal_transaction.description = f"Withdrawal rejected - amount returned to balance"
            withdrawal_transaction.save()

        # Create refund transaction for returned amount
        WalletTransaction.objects.create(
            user=user,
            transaction_type='refund',
            amount=withdrawal.amount,
            status='completed',
            payout_status='completed',
            description=f"Refund for rejected withdrawal #{withdrawal.id}",
            withdrawal_request_id=str(withdrawal.id)
        )

    withdrawal.save()

    return Response({
        'message': f'Withdrawal request {action} successfully',
        'withdrawal_id': str(withdrawal.id),
        'status': withdrawal.status
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_payments(request):
    """Get all payments for admin"""

    try:
        # Check if user is admin/staff
        if not request.user.is_staff:
            return Response(
                {'error': 'Admin access required'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get filter parameters
        status_filter = request.GET.get('status', '')
        method_filter = request.GET.get('method', '')
        search = request.GET.get('search', '')

        # Build queryset
        queryset = Payment.objects.select_related('buyer', 'seller', 'auction').all()

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        if method_filter:
            queryset = queryset.filter(payment_method=method_filter)

        if search:
            queryset = queryset.filter(
                models.Q(buyer__username__icontains=search) |
                models.Q(seller__username__icontains=search) |
                models.Q(auction__title__icontains=search)
            )

        # Serialize data
        payments = []
        for payment in queryset.order_by('-created_at'):
            try:
                payments.append({
                    'id': str(payment.id),
                    'auction': {
                        'id': payment.auction.id,
                        'title': payment.auction.title,
                    },
                    'buyer': {
                        'username': payment.buyer.username,
                        'email': payment.buyer.email,
                    },
                    'seller': {
                        'username': payment.seller.username,
                        'email': payment.seller.email,
                    },
                    'amount': float(payment.amount),
                    'platform_fee': float(payment.platform_fee),
                    'seller_amount': float(payment.seller_amount),
                    'payment_method': payment.payment_method,
                    'payment_status': payment.status,  # Use 'status' field
                    'stripe_payment_intent_id': payment.stripe_payment_intent_id or '',
                    'paypal_payment_id': '',  # Not available in this model
                    'created_at': payment.created_at.isoformat(),
                    'completed_at': payment.paid_at.isoformat() if payment.paid_at else None,  # Use 'paid_at' field
                })
            except Exception as e:
                # Skip payments with missing related objects
                print(f"Error serializing payment {payment.id}: {e}")
                continue

        return Response({
            'payments': payments,
            'total_count': len(payments)
        })

    except Exception as e:
        print(f"Error in admin_payments view: {e}")
        return Response(
            {'error': f'Internal server error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_payment_methods(request):
    """Get all payment methods for admin review"""

    try:
        # Check if user is admin/staff
        if not request.user.is_staff:
            return Response(
                {'error': 'Admin access required'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get filter parameters
        status_filter = request.GET.get('status', '')
        type_filter = request.GET.get('type', '')
        search = request.GET.get('search', '')

        # Build queryset
        queryset = PayoutMethod.objects.select_related('user').all()

        if status_filter:
            if status_filter == 'pending':
                queryset = queryset.filter(is_verified=False, verification_status='pending')
            elif status_filter == 'verified':
                queryset = queryset.filter(is_verified=True, verification_status='verified')
            elif status_filter == 'rejected':
                queryset = queryset.filter(verification_status='rejected')

        if type_filter:
            queryset = queryset.filter(payout_type=type_filter)

        if search:
            queryset = queryset.filter(
                models.Q(user__username__icontains=search) |
                models.Q(user__email__icontains=search) |
                models.Q(paypal_email__icontains=search) |
                models.Q(payoneer_email__icontains=search)
            )

        # Get statistics
        total_count = PayoutMethod.objects.count()
        pending_count = PayoutMethod.objects.filter(is_verified=False, verification_status='pending').count()
        verified_count = PayoutMethod.objects.filter(is_verified=True, verification_status='verified').count()
        rejected_count = PayoutMethod.objects.filter(verification_status='rejected').count()

        # Serialize data
        payment_methods = []
        for method in queryset.order_by('-created_at'):
            try:
                payment_methods.append({
                    'id': str(method.id),
                    'user': {
                        'username': method.user.username,
                        'email': method.user.email,
                        'user_type': method.user.user_type,
                    },
                    'payout_type': method.payout_type,
                    'is_default': method.is_default,
                    'is_verified': method.is_verified,
                    'verification_status': getattr(method, 'verification_status', 'pending'),
                    'paypal_email': method.paypal_email,
                    'stripe_account_id': method.stripe_account_id,
                    'bank_name': method.bank_name,
                    'account_holder_name': method.account_holder_name,
                    'account_number': method.account_number,
                    'payoneer_email': method.payoneer_email,
                    'created_at': method.created_at.isoformat(),
                })
            except Exception as e:
                print(f"Error serializing payment method {method.id}: {e}")
                continue

        return Response({
            'payment_methods': payment_methods,
            'total_count': total_count,
            'pending_count': pending_count,
            'verified_count': verified_count,
            'rejected_count': rejected_count,
        })

    except Exception as e:
        print(f"Error in admin_payment_methods view: {e}")
        return Response(
            {'error': f'Internal server error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def review_payment_method(request, method_id):
    """Review (approve/reject) a payment method"""

    try:
        # Check if user is admin/staff
        if not request.user.is_staff:
            return Response(
                {'error': 'Admin access required'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get payment method
        try:
            payment_method = PayoutMethod.objects.get(id=method_id)
        except PayoutMethod.DoesNotExist:
            return Response(
                {'error': 'Payment method not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        action = request.data.get('action')
        notes = request.data.get('notes', '')

        if action not in ['approve', 'reject']:
            return Response(
                {'error': 'Invalid action. Must be "approve" or "reject"'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update payment method
        if action == 'approve':
            payment_method.is_verified = True
            payment_method.verification_status = 'verified'
        else:  # reject
            payment_method.is_verified = False
            payment_method.verification_status = 'rejected'

        # Add review notes if provided
        if notes:
            payment_method.review_notes = notes

        payment_method.reviewed_by = request.user
        payment_method.reviewed_at = timezone.now()
        payment_method.save()

        return Response({
            'message': f'Payment method {action}d successfully',
            'payment_method_id': str(payment_method.id),
            'status': payment_method.verification_status,
            'is_verified': payment_method.is_verified
        })

    except Exception as e:
        print(f"Error in review_payment_method view: {e}")
        return Response(
            {'error': f'Internal server error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_transactions(request):
    """Get all wallet transactions for admin"""

    # Check if user is admin/staff
    if not request.user.is_staff:
        return Response(
            {'error': 'Admin access required'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Get filter parameters
    type_filter = request.GET.get('type', '')
    status_filter = request.GET.get('status', '')
    search = request.GET.get('search', '')

    # Build queryset
    queryset = WalletTransaction.objects.select_related('user', 'auction').all()

    if type_filter:
        queryset = queryset.filter(transaction_type=type_filter)

    if status_filter:
        queryset = queryset.filter(status=status_filter)

    if search:
        queryset = queryset.filter(
            models.Q(user__username__icontains=search) |
            models.Q(user__email__icontains=search)
        )

    # Serialize data
    transactions = []
    for transaction in queryset.order_by('-created_at'):
        transactions.append({
            'id': str(transaction.id),
            'user': {
                'username': transaction.user.username,
                'email': transaction.user.email,
                'user_type': transaction.user.user_type,
            },
            'transaction_type': transaction.transaction_type,
            'amount': float(transaction.amount),
            'description': transaction.description,
            'status': transaction.status,
            'payout_status': transaction.payout_status,  # Include payout status
            'auction': {
                'id': transaction.auction.id,
                'title': transaction.auction.title,
            } if transaction.auction else None,
            'external_transaction_id': transaction.external_transaction_id,
            'created_at': transaction.created_at.isoformat(),
            'updated_at': transaction.updated_at.isoformat(),
        })

    return Response({
        'transactions': transactions,
        'total_count': len(transactions)
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_dashboard_stats(request):
    """Get dashboard statistics for admin"""

    # Check if user is admin/staff
    if not request.user.is_staff:
        return Response(
            {'error': 'Admin access required'},
            status=status.HTTP_403_FORBIDDEN
        )

    from django.db.models import Sum, Count
    from datetime import datetime, timedelta

    today = timezone.now().date()

    # Withdrawal stats
    pending_withdrawals = WithdrawalRequest.objects.filter(status='pending').count()
    approved_withdrawals_today = WithdrawalRequest.objects.filter(
        status__in=['approved', 'completed'],
        approved_at__date=today
    ).count()
    rejected_withdrawals_today = WithdrawalRequest.objects.filter(
        status='rejected',
        updated_at__date=today
    ).count()
    total_withdrawals_amount = WithdrawalRequest.objects.filter(
        status__in=['approved', 'completed']
    ).aggregate(total=Sum('payout_amount'))['total'] or 0

    # Payment stats
    total_payments = Payment.objects.count()
    completed_payments = Payment.objects.filter(status='completed').count()
    pending_payments = Payment.objects.filter(status='pending').count()
    total_revenue = Payment.objects.filter(
        status='completed'
    ).aggregate(total=Sum('platform_fee'))['total'] or 0

    # Transaction stats
    total_transactions = WalletTransaction.objects.count()
    deposits_count = WalletTransaction.objects.filter(transaction_type='deposit').count()
    withdrawals_count = WalletTransaction.objects.filter(transaction_type='withdrawal').count()
    payments_count = WalletTransaction.objects.filter(transaction_type='payment').count()

    return Response({
        'withdrawals': {
            'pending': pending_withdrawals,
            'approved_today': approved_withdrawals_today,
            'rejected_today': rejected_withdrawals_today,
            'total_amount': float(total_withdrawals_amount),
        },
        'payments': {
            'total': total_payments,
            'completed': completed_payments,
            'pending': pending_payments,
            'total_revenue': float(total_revenue),
        },
        'transactions': {
            'total': total_transactions,
            'deposits': deposits_count,
            'withdrawals': withdrawals_count,
            'payments': payments_count,
        }
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_withdrawal_requests(request):
    """Get user's withdrawal requests with status"""

    try:
        # Get user's withdrawal requests
        withdrawals = WithdrawalRequest.objects.filter(
            user=request.user
        ).select_related('payout_method').order_by('-created_at')

        withdrawal_list = []
        for withdrawal in withdrawals:
            # Get related transaction
            transaction = WalletTransaction.objects.filter(
                withdrawal_request_id=str(withdrawal.id),
                transaction_type='withdrawal'
            ).first()

            withdrawal_list.append({
                'id': str(withdrawal.id),
                'amount': float(withdrawal.amount),
                'platform_fee': float(withdrawal.platform_fee),
                'payout_amount': float(withdrawal.payout_amount),
                'status': withdrawal.status,
                'payout_method': {
                    'type': withdrawal.payout_method.payout_type,
                    'details': _get_payout_method_display(withdrawal.payout_method)
                },
                'payout_status': transaction.payout_status if transaction else 'unknown',
                'created_at': withdrawal.created_at.isoformat(),
                'approved_at': withdrawal.approved_at.isoformat() if withdrawal.approved_at else None,
                'rejected_at': withdrawal.rejected_at.isoformat() if withdrawal.rejected_at else None,
                'completed_at': withdrawal.completed_at.isoformat() if withdrawal.completed_at else None,
                'external_transaction_id': transaction.external_transaction_id if transaction else None,
            })

        return Response({
            'withdrawals': withdrawal_list,
            'total_count': len(withdrawal_list)
        })

    except Exception as e:
        print(f"Error in user_withdrawal_requests view: {e}")
        return Response(
            {'error': f'Internal server error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _get_payout_method_display(payout_method):
    """Get display string for payout method"""
    if payout_method.payout_type == 'paypal':
        return payout_method.paypal_email or 'PayPal'
    elif payout_method.payout_type == 'stripe':
        return f"Stripe Account: {payout_method.stripe_account_id or 'Not set'}"
    elif payout_method.payout_type == 'bank_transfer':
        return f"{payout_method.bank_name or 'Bank'}"
    elif payout_method.payout_type == 'payoneer':
        return payout_method.payoneer_email or 'Payoneer'
    elif payout_method.payout_type == 'telr':
        return f"Telr: {payout_method.telr_account_email or payout_method.telr_merchant_id or 'Telr Account'}"
    else:
        return payout_method.get_payout_type_display()


# Invoice API Views

class InvoiceDetailView(APIView):
    """Get invoice details for a payment"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get invoice details",
        description="Get invoice details for a specific payment"
    )
    def get(self, request, payment_id):
        try:
            logger.info(f"InvoiceDetailView: Getting invoice for payment {payment_id}")

            # Get language parameter
            language = request.GET.get('language', 'en')
            logger.info(f"InvoiceDetailView: Language requested: {language}")

            # Get payment and check permissions
            payment = Payment.objects.get(id=payment_id)
            logger.info(f"InvoiceDetailView: Found payment {payment.id}")

            # Check if user is buyer or seller
            if request.user != payment.buyer and request.user != payment.seller:
                logger.warning(f"InvoiceDetailView: Permission denied for user {request.user.id}")
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Get or create invoice
            try:
                invoice = payment.invoice
                logger.info(f"InvoiceDetailView: Found existing invoice {invoice.id}")

                # Update language if different
                if invoice.language != language:
                    invoice.language = language
                    invoice.save()
                    logger.info(f"InvoiceDetailView: Updated invoice language to {language}")

                # Update status if payment is completed but invoice is not paid
                if payment.status == 'completed' and invoice.status != 'paid':
                    invoice.status = 'paid'
                    invoice.paid_date = payment.paid_at
                    invoice.save()
                    logger.info(f"InvoiceDetailView: Updated invoice status to paid")

            except Invoice.DoesNotExist:
                logger.info(f"InvoiceDetailView: Creating new invoice for payment {payment.id}")
                # Create invoice if it doesn't exist
                payment_service = PaymentService()
                try:
                    invoice = payment_service.create_invoice(payment, language=language)
                    logger.info(f"InvoiceDetailView: Created invoice {invoice.id}")
                except Exception as e:
                    logger.error(f"InvoiceDetailView: Error creating invoice: {str(e)}")
                    import traceback
                    logger.error(f"InvoiceDetailView: Traceback: {traceback.format_exc()}")
                    return Response(
                        {'error': 'Failed to create invoice'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            try:
                serializer = InvoiceSerializer(invoice)
                logger.info(f"InvoiceDetailView: Serialization successful")
                return Response(serializer.data)
            except Exception as e:
                logger.error(f"InvoiceDetailView: Error serializing invoice: {str(e)}")
                import traceback
                logger.error(f"InvoiceDetailView: Serialization traceback: {traceback.format_exc()}")
                return Response(
                    {'error': 'Failed to serialize invoice'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        except Payment.DoesNotExist:
            logger.error(f"InvoiceDetailView: Payment {payment_id} not found")
            return Response(
                {'error': 'Payment not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"InvoiceDetailView: Unexpected error for payment {payment_id}: {str(e)}")
            import traceback
            logger.error(f"InvoiceDetailView: Traceback: {traceback.format_exc()}")
            return Response(
                {'error': f'Failed to get invoice: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InvoicePDFDownloadView(APIView):
    """Download invoice PDF"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Download invoice PDF",
        description="Download PDF file for a specific invoice"
    )
    def get(self, request, payment_id):
        try:
            logger.info(f"InvoicePDFDownloadView: Getting invoice for payment {payment_id}")

            # Get payment and check permissions
            payment = Payment.objects.get(id=payment_id)
            logger.info(f"InvoicePDFDownloadView: Found payment {payment.id}")

            # Check if user is buyer or seller
            if request.user != payment.buyer and request.user != payment.seller:
                logger.warning(f"InvoicePDFDownloadView: Permission denied for user {request.user.id}")
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Get or create invoice
            try:
                invoice = payment.invoice
                logger.info(f"InvoicePDFDownloadView: Found existing invoice {invoice.id}")
            except Invoice.DoesNotExist:
                logger.info(f"InvoicePDFDownloadView: Creating new invoice for payment {payment.id}")
                # Create invoice if it doesn't exist
                payment_service = PaymentService()
                try:
                    invoice = payment_service.create_invoice(payment)
                    logger.info(f"InvoicePDFDownloadView: Created invoice {invoice.id}")
                except Exception as e:
                    logger.error(f"InvoicePDFDownloadView: Error creating invoice: {str(e)}")
                    import traceback
                    logger.error(f"InvoicePDFDownloadView: Traceback: {traceback.format_exc()}")
                    return Response(
                        {'error': 'Failed to create invoice'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

            # Generate PDF if not exists
            if not invoice.pdf_file:
                logger.info(f"InvoicePDFDownloadView: Generating PDF for invoice {invoice.id}")
                from .pdf_service import InvoicePDFService
                pdf_service = InvoicePDFService()
                try:
                    pdf_service.save_invoice_pdf(invoice)
                    logger.info(f"InvoicePDFDownloadView: PDF generated successfully")
                except Exception as e:
                    logger.error(f"Error generating PDF for invoice {invoice.id}: {str(e)}")
                    import traceback
                    logger.error(f"PDF generation traceback: {traceback.format_exc()}")
                    return Response(
                        {'error': 'Failed to generate PDF'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
            else:
                logger.info(f"InvoicePDFDownloadView: PDF already exists")

            # Return PDF URL
            if invoice.pdf_file:
                pdf_url = request.build_absolute_uri(invoice.pdf_file.url)
                logger.info(f"InvoicePDFDownloadView: Returning PDF URL: {pdf_url}")
                return Response({
                    'pdf_url': pdf_url,
                    'invoice_number': invoice.invoice_number,
                    'filename': f"invoice_{invoice.invoice_number}.pdf"
                })
            else:
                logger.error(f"InvoicePDFDownloadView: PDF file not available after generation")
                return Response(
                    {'error': 'PDF not available'},
                    status=status.HTTP_404_NOT_FOUND
                )

        except Payment.DoesNotExist:
            logger.error(f"InvoicePDFDownloadView: Payment {payment_id} not found")
            return Response(
                {'error': 'Payment not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"InvoicePDFDownloadView: Unexpected error for payment {payment_id}: {str(e)}")
            import traceback
            logger.error(f"InvoicePDFDownloadView: Traceback: {traceback.format_exc()}")
            return Response(
                {'error': f'Failed to download PDF: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
