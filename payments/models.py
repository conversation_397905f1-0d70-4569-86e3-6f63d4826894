from django.db import models
from django.conf import settings
from decimal import Decimal
import uuid


class WalletTransaction(models.Model):
    """Wallet transaction model"""

    TRANSACTION_TYPE_CHOICES = [
        ('deposit', 'Deposit'),
        ('withdrawal', 'Withdrawal'),
        ('payment', 'Payment'),
        ('refund', 'Refund'),
        ('reserve', 'Reserve'),
        ('release', 'Release'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='wallet_transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    description = models.TextField(blank=True)

    # External payment references
    stripe_payment_intent_id = models.CharField(max_length=200, blank=True, null=True)
    stripe_charge_id = models.CharField(max_length=200, blank=True, null=True)
    external_transaction_id = models.CharField(max_length=200, blank=True, null=True, help_text="External transaction ID from payment processor")

    # Payout status for withdrawal transactions
    PAYOUT_STATUS_CHOICES = [
        ('pending_approval', 'Pending Admin Approval'),
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('approved', 'Approved'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('failed', 'Failed'),
    ]

    payout_status = models.CharField(
        max_length=20,
        choices=PAYOUT_STATUS_CHOICES,
        null=True,
        blank=True,
        help_text="Status of payout for withdrawal transactions"
    )

    # Related objects
    auction = models.ForeignKey('auctions.Auction', on_delete=models.SET_NULL, null=True, blank=True)

    # External references
    stripe_payment_intent_id = models.CharField(max_length=200, blank=True, null=True)
    external_transaction_id = models.CharField(max_length=200, blank=True, null=True)
    withdrawal_request_id = models.CharField(max_length=200, blank=True, null=True)  # Link to withdrawal request

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_transaction_type_display()} - ${self.amount} - {self.user.username}"

    class Meta:
        ordering = ['-created_at']


class Payment(models.Model):
    """Payment model for auction purchases"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('cancelled', 'Cancelled'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('wallet', 'Wallet'),
        ('stripe', 'Credit/Debit Card'),
        ('bank_transfer', 'Bank Transfer'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    auction = models.OneToOneField('auctions.Auction', on_delete=models.CASCADE, related_name='payment')
    buyer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payments')
    seller = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_payments'
    )

    amount = models.DecimalField(max_digits=12, decimal_places=2)
    platform_fee = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    seller_amount = models.DecimalField(max_digits=12, decimal_places=2)

    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # External payment references
    stripe_payment_intent_id = models.CharField(max_length=200, blank=True, null=True)
    stripe_charge_id = models.CharField(max_length=200, blank=True, null=True)

    # Timestamps
    payment_deadline = models.DateTimeField()
    paid_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Payment for {self.auction.title} - ${self.amount}"

    @property
    def is_overdue(self):
        from django.utils import timezone
        return timezone.now() > self.payment_deadline and self.status == 'pending'

    class Meta:
        ordering = ['-created_at']


class Invoice(models.Model):
    """Invoice model for payments"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment = models.OneToOneField(Payment, on_delete=models.CASCADE, related_name='invoice')
    invoice_number = models.CharField(max_length=50, unique=True)

    # Invoice details
    buyer_name = models.CharField(max_length=200)
    buyer_email = models.EmailField()
    buyer_address = models.TextField(blank=True)

    seller_name = models.CharField(max_length=200)
    seller_email = models.EmailField()
    seller_address = models.TextField(blank=True)

    # Amounts
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # PDF file
    pdf_file = models.FileField(upload_to='invoices/', null=True, blank=True)

    # Timestamps
    issue_date = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField()
    paid_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.payment.auction.title}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            # Generate invoice number
            from django.utils import timezone
            timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
            self.invoice_number = f"INV-{timestamp}-{str(self.id)[:8]}"
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-issue_date']


class Refund(models.Model):
    """Refund model"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    REASON_CHOICES = [
        ('buyer_request', 'Buyer Request'),
        ('seller_cancellation', 'Seller Cancellation'),
        ('dispute_resolution', 'Dispute Resolution'),
        ('system_error', 'System Error'),
        ('payment_timeout', 'Payment Timeout'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='refunds')
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    reason = models.CharField(max_length=30, choices=REASON_CHOICES)
    description = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # External refund references
    stripe_refund_id = models.CharField(max_length=200, blank=True, null=True)

    requested_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_refunds'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Refund ${self.amount} for {self.payment.auction.title}"

    class Meta:
        ordering = ['-created_at']


class PayoutMethod(models.Model):
    """User payout methods for withdrawals"""

    PAYOUT_TYPES = [
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
        ('bank_transfer', 'Bank Transfer'),
        ('payoneer', 'Payoneer'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payout_methods')

    payout_type = models.CharField(max_length=20, choices=PAYOUT_TYPES)
    is_default = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)

    # PayPal
    paypal_email = models.EmailField(blank=True, null=True)

    # Stripe
    stripe_account_id = models.CharField(max_length=255, blank=True, null=True)

    # Bank Transfer
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    account_holder_name = models.CharField(max_length=100, blank=True, null=True)
    account_number = models.CharField(max_length=50, blank=True, null=True)
    routing_number = models.CharField(max_length=50, blank=True, null=True)
    iban = models.CharField(max_length=50, blank=True, null=True)
    swift_code = models.CharField(max_length=20, blank=True, null=True)

    # Payoneer
    payoneer_email = models.EmailField(blank=True, null=True)

    # Verification fields
    VERIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('verified', 'Verified'),
        ('rejected', 'Rejected'),
    ]

    verification_status = models.CharField(
        max_length=20,
        choices=VERIFICATION_STATUS_CHOICES,
        default='pending'
    )
    review_notes = models.TextField(blank=True, null=True)
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_payment_methods'
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.get_payout_type_display()}"

    class Meta:
        ordering = ['-created_at']


class WithdrawalRequest(models.Model):
    """User withdrawal requests"""

    STATUS_CHOICES = [
        ('pending', 'Pending Admin Approval'),
        ('approved', 'Approved'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('failed', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='withdrawal_requests')
    payout_method = models.ForeignKey(PayoutMethod, on_delete=models.CASCADE)

    amount = models.DecimalField(max_digits=10, decimal_places=2)
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('2.00'))  # $2 withdrawal fee
    payout_amount = models.DecimalField(max_digits=10, decimal_places=2)

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    admin_notes = models.TextField(blank=True)

    # External transaction IDs
    external_transaction_id = models.CharField(max_length=255, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Admin who processed the request
    processed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='processed_withdrawals')

    def save(self, *args, **kwargs):
        if not self.payout_amount:
            self.payout_amount = self.amount - self.platform_fee
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Withdrawal {self.id} - {self.user.username} - ${self.amount}"

    class Meta:
        ordering = ['-created_at']
