from django.urls import path
from .views import (
    WalletBalanceView, WalletTransactionsView, WalletTopUpView,
    PaymentListView, PaymentDetailView, ProcessPaymentView,
    WalletConfirmTopUpView, WalletWithdrawView, StripeConfigView,
    PayoutMethodListCreateView, request_withdrawal, check_payout_methods,
    admin_withdrawal_requests, admin_process_withdrawal, admin_payments,
    admin_payment_methods, review_payment_method, admin_transactions, admin_dashboard_stats,
    user_withdrawal_requests
)
from . import views

app_name = 'payments'

urlpatterns = [
    # Wallet endpoints
    path('wallet/balance/', WalletBalanceView.as_view(), name='wallet_balance'),
    path('wallet/transactions/', WalletTransactionsView.as_view(), name='wallet_transactions'),
    path('wallet/topup/', WalletTopUpView.as_view(), name='wallet_topup'),
    path('wallet/confirm/', WalletConfirmTopUpView.as_view(), name='wallet_confirm_topup'),
    path('wallet/withdraw/', WalletWithdrawView.as_view(), name='wallet_withdraw'),

    # Payment endpoints
    path('', PaymentListView.as_view(), name='payment_list'),
    path('<uuid:pk>/', PaymentDetailView.as_view(), name='payment_detail'),
    path('process/<int:auction_id>/', ProcessPaymentView.as_view(), name='process_payment'),

    # Stripe endpoints
    path('stripe/config/', StripeConfigView.as_view(), name='stripe_config'),

    # Payout methods endpoints
    path('payout-methods/', PayoutMethodListCreateView.as_view(), name='payout_methods'),
    path('payout-methods/check/', check_payout_methods, name='check_payout_methods'),
    path('payout-methods/<str:payout_method_id>/set-default/', views.set_default_payout_method, name='set_default_payout_method'),
    path('payout-methods/<str:payout_method_id>/test/', views.test_payout_method, name='test_payout_method'),
    path('payout-methods/<str:payout_method_id>/', views.PayoutMethodDetailView.as_view(), name='payout_method_detail'),

    # Withdrawal endpoints
    path('withdrawals/request/', request_withdrawal, name='request_withdrawal'),
    path('withdrawals/my-requests/', user_withdrawal_requests, name='user_withdrawal_requests'),

    # Admin endpoints
    path('admin/withdrawals/', admin_withdrawal_requests, name='admin_withdrawal_requests'),
    path('admin/withdrawals/<str:withdrawal_id>/process/', admin_process_withdrawal, name='admin_process_withdrawal'),
    path('admin/payments/', admin_payments, name='admin_payments'),
    path('admin/payment-methods/', admin_payment_methods, name='admin_payment_methods'),
    path('admin/payment-methods/<uuid:method_id>/review/', review_payment_method, name='review_payment_method'),
    path('admin/transactions/', admin_transactions, name='admin_transactions'),
    path('admin/dashboard-stats/', admin_dashboard_stats, name='admin_dashboard_stats'),
]
