import stripe
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
from .models import Payment, WalletTransaction, Invoice, Refund
from accounts.models import User
import logging

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class PaymentService:
    """Service for handling payments and wallet operations"""
    
    def __init__(self):
        self.platform_fee_percentage = Decimal('0.05')  # 5% platform fee
    
    def create_payment(self, auction, buyer):
        """Create a payment record for an auction"""
        try:
            amount = auction.current_price
            platform_fee = amount * self.platform_fee_percentage
            seller_amount = amount - platform_fee
            
            payment = Payment.objects.create(
                auction=auction,
                buyer=buyer,
                seller=auction.seller,
                amount=amount,
                platform_fee=platform_fee,
                seller_amount=seller_amount,
                payment_method='wallet',  # Default to wallet
                payment_deadline=auction.payment_deadline
            )
            
            # Create invoice
            self.create_invoice(payment)
            
            return payment
            
        except Exception as e:
            logger.error(f"Failed to create payment for auction {auction.id}: {str(e)}")
            raise
    
    def process_wallet_payment(self, payment):
        """Process payment using user's wallet"""
        try:
            buyer = payment.buyer
            amount = payment.amount
            
            # Check wallet balance
            if buyer.wallet_balance < amount:
                raise ValueError("Insufficient wallet balance")
            
            # Reserve funds
            self.reserve_wallet_funds(buyer, amount, payment.auction)
            
            # Update payment status
            payment.status = 'completed'
            payment.paid_at = timezone.now()
            payment.save()

            # Update invoice status
            self.update_invoice_status(payment)

            # Transfer funds to seller
            self.transfer_to_seller(payment)

            # Mark auction as payment received
            payment.auction.payment_received = True

            # For direct buy auctions, end the auction after successful payment
            if payment.auction.auction_type == 'buy_now' and payment.auction.status == 'live':
                payment.auction.status = 'ended'

                # Send notifications to winner and seller
                from notifications.services import NotificationService
                notification_service = NotificationService()

                # Notify winner (buyer)
                notification_service.send_notification(
                    user=payment.auction.winner,
                    notification_type='auction_won',
                    context={'auction': payment.auction}
                )

                # Notify seller
                notification_service.send_notification(
                    user=payment.auction.seller,
                    notification_type='auction_sold',
                    context={'auction': payment.auction, 'winner': payment.auction.winner}
                )

            payment.auction.save()

            # Create delivery tracking
            self.create_delivery_tracking(payment)

            # Send payment notification to seller
            self.send_payment_notification(payment)

            # Generate and send invoice
            self.generate_and_send_invoice(payment)

            return payment
            
        except Exception as e:
            logger.error(f"Failed to process wallet payment {payment.id}: {str(e)}")
            payment.status = 'failed'
            payment.save()
            raise
    
    def process_stripe_payment(self, payment, payment_method_id):
        """Process payment using Stripe"""
        try:
            # Create payment intent
            intent = stripe.PaymentIntent.create(
                amount=int(payment.amount * 100),  # Convert to cents
                currency='usd',
                payment_method=payment_method_id,
                confirmation_method='manual',
                confirm=True,
                metadata={
                    'payment_id': str(payment.id),
                    'auction_id': str(payment.auction.id),
                    'buyer_id': str(payment.buyer.id)
                }
            )
            
            payment.stripe_payment_intent_id = intent.id
            
            if intent.status == 'succeeded':
                payment.status = 'completed'
                payment.paid_at = timezone.now()
                payment.stripe_charge_id = intent.charges.data[0].id

                # Update invoice status
                self.update_invoice_status(payment)

                # Transfer funds to seller
                self.transfer_to_seller(payment)

                # Mark auction as payment received
                payment.auction.payment_received = True

                # For direct buy auctions, end the auction after successful payment
                if payment.auction.auction_type == 'buy_now' and payment.auction.status == 'live':
                    payment.auction.status = 'ended'

                    # Send notifications to winner and seller
                    from notifications.services import NotificationService
                    notification_service = NotificationService()

                    # Notify winner (buyer)
                    notification_service.send_notification(
                        user=payment.auction.winner,
                        notification_type='auction_won',
                        context={'auction': payment.auction}
                    )

                    # Notify seller
                    notification_service.send_notification(
                        user=payment.auction.seller,
                        notification_type='auction_sold',
                        context={'auction': payment.auction, 'winner': payment.auction.winner}
                    )

                payment.auction.save()

                # Create delivery tracking
                self.create_delivery_tracking(payment)

                # Send payment notification to seller
                self.send_payment_notification(payment)

                # Generate and send invoice
                self.generate_and_send_invoice(payment)
                
            elif intent.status == 'requires_action':
                payment.status = 'processing'
                
            payment.save()
            
            return intent
            
        except stripe.error.CardError as e:
            logger.error(f"Stripe card error for payment {payment.id}: {str(e)}")
            payment.status = 'failed'
            payment.save()
            raise
        except Exception as e:
            logger.error(f"Failed to process Stripe payment {payment.id}: {str(e)}")
            payment.status = 'failed'
            payment.save()
            raise
    
    def reserve_wallet_funds(self, user, amount, auction):
        """Reserve funds in user's wallet"""
        try:
            if user.wallet_balance < amount:
                raise ValueError("Insufficient wallet balance")

            # Deduct from wallet balance
            user.wallet_balance -= amount
            user.save()

            # Create transaction record - this is the actual payment deduction
            WalletTransaction.objects.create(
                user=user,
                transaction_type='payment',  # Changed from 'reserve' to 'payment'
                amount=-amount,  # Negative amount to show deduction
                status='completed',
                description=f"Payment for auction: {auction.title}",
                auction=auction
            )

            return True

        except Exception as e:
            logger.error(f"Failed to reserve wallet funds for user {user.id}: {str(e)}")
            raise
    
    def release_wallet_funds(self, user, amount, auction, reason="Funds released"):
        """Release reserved funds back to user's wallet"""
        try:
            # Add back to wallet balance
            user.wallet_balance += amount
            user.save()
            
            # Create transaction record
            WalletTransaction.objects.create(
                user=user,
                transaction_type='release',
                amount=amount,
                status='completed',
                description=reason,
                auction=auction
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to release wallet funds for user {user.id}: {str(e)}")
            raise
    
    def transfer_to_seller(self, payment):
        """Transfer funds to seller's wallet"""
        try:
            seller = payment.seller
            amount = payment.seller_amount

            # Add to seller's wallet
            seller.wallet_balance += amount
            seller.save()

            # Create transaction record - positive amount for seller
            WalletTransaction.objects.create(
                user=seller,
                transaction_type='payment',
                amount=amount,  # Positive amount to show credit
                status='completed',
                description=f"Payment received for auction: {payment.auction.title}",
                auction=payment.auction
            )

            return True

        except Exception as e:
            logger.error(f"Failed to transfer funds to seller {payment.seller.id}: {str(e)}")
            raise

    def send_payment_notification(self, payment):
        """Send payment received notification to seller"""
        try:
            from notifications.services import NotificationService

            print(f"🔍 DEBUG: Sending payment notification to seller: {payment.seller.username}")
            print(f"   Payment ID: {payment.id}")
            print(f"   Auction: {payment.auction.title}")
            print(f"   Amount: ${payment.amount}")
            print(f"   Seller amount: ${payment.seller_amount}")

            # Send notification to seller
            notification_service = NotificationService()
            notifications = notification_service.send_notification(
                user=payment.seller,
                notification_type='payment_received',
                context={
                    'payment': payment,
                    'auction': payment.auction,
                    'buyer': payment.buyer,
                    'amount': payment.amount,
                    'seller_amount': payment.seller_amount,
                }
            )

            logger.info(f"Payment notification sent to seller {payment.seller.id} for payment {payment.id}")
            print(f"💰 Payment notifications sent to seller: {len(notifications) if notifications else 0}")
            if notifications:
                for notif in notifications:
                    print(f"   📱 {notif.channel}: {notif.status} - {notif.title}")
            return True

        except Exception as e:
            logger.error(f"Failed to send payment notification for payment {payment.id}: {str(e)}")
            print(f"❌ Failed to send payment notification: {e}")
            import traceback
            traceback.print_exc()
            # Don't raise exception as this is not critical for payment processing
            return False
    
    def add_wallet_funds(self, user, amount, description="Wallet top-up"):
        """Add funds to user's wallet"""
        try:
            from decimal import Decimal

            # Convert amount to Decimal to match wallet_balance field type
            decimal_amount = Decimal(str(amount))
            logger.info(f"Converting amount {amount} to Decimal: {decimal_amount}")

            user.wallet_balance += decimal_amount
            user.save()
            logger.info(f"User {user.id} wallet balance updated to: {user.wallet_balance}")

            # Create transaction record - positive amount for deposit
            WalletTransaction.objects.create(
                user=user,
                transaction_type='deposit',
                amount=decimal_amount,  # Positive amount for deposit
                status='completed',
                description=description
            )
            logger.info(f"Transaction record created for user {user.id}")

            return True

        except Exception as e:
            logger.error(f"Failed to add wallet funds for user {user.id}: {str(e)}")
            raise
    
    def create_invoice(self, payment, language='en'):
        """Create an invoice for a payment"""
        try:
            # Determine invoice status based on payment status
            invoice_status = 'draft'
            paid_date = None

            if payment.status == 'completed':
                invoice_status = 'paid'
                paid_date = payment.paid_at
            elif payment.status == 'pending':
                invoice_status = 'sent'

            invoice = Invoice.objects.create(
                payment=payment,
                buyer_name=payment.buyer.get_full_name() or payment.buyer.username,
                buyer_email=payment.buyer.email,
                buyer_address=getattr(payment.buyer, 'address', '') or "",
                seller_name=payment.seller.get_full_name() or payment.seller.username,
                seller_email=payment.seller.email,
                seller_address=getattr(payment.seller, 'address', '') or "",
                subtotal=payment.amount - payment.platform_fee,
                total_amount=payment.amount,
                due_date=payment.payment_deadline,
                status=invoice_status,
                paid_date=paid_date,
                language=language
            )

            return invoice

        except Exception as e:
            logger.error(f"Failed to create invoice for payment {payment.id}: {str(e)}")
            raise

    def update_invoice_status(self, payment):
        """Update invoice status when payment status changes"""
        try:
            if hasattr(payment, 'invoice'):
                invoice = payment.invoice

                if payment.status == 'completed' and invoice.status != 'paid':
                    invoice.status = 'paid'
                    invoice.paid_date = payment.paid_at
                    invoice.save()
                    logger.info(f"Updated invoice {invoice.id} status to paid")

        except Exception as e:
            logger.error(f"Failed to update invoice status for payment {payment.id}: {str(e)}")

    def generate_and_send_invoice(self, payment):
        """Generate PDF invoice and send via WhatsApp"""
        try:
            # Get the invoice (should already exist from create_payment)
            invoice = payment.invoice

            # Generate PDF if not already generated
            if not invoice.pdf_file:
                from .pdf_service import InvoicePDFService
                pdf_service = InvoicePDFService()
                pdf_service.save_invoice_pdf(invoice)

                # Update invoice status
                invoice.status = 'sent'
                invoice.save()

            # Send WhatsApp notifications
            self.send_invoice_whatsapp_notifications(invoice)

            logger.info(f"Invoice PDF generated and sent for payment {payment.id}")

        except Exception as e:
            logger.error(f"Failed to generate and send invoice for payment {payment.id}: {str(e)}")
            # Don't raise exception to avoid breaking payment flow

    def send_invoice_whatsapp_notifications(self, invoice):
        """Send invoice via WhatsApp to both buyer and seller"""
        try:
            from notifications.services import NotificationService
            from django.conf import settings

            notification_service = NotificationService()

            # Prepare invoice URL
            if invoice.pdf_file:
                invoice_url = f"{settings.SITE_URL}{invoice.pdf_file.url}"
            else:
                invoice_url = None

            # Send to buyer
            if invoice.payment.buyer.phone_number:
                buyer_message = f"""
🧾 *Invoice for Your Fish Purchase*

Hello {invoice.buyer_name},

Thank you for your purchase! Your invoice #{invoice.invoice_number} is ready.

*Auction Details:*
• Item: {invoice.payment.auction.title}
• Amount: ${invoice.total_amount:.2f}
• Status: {invoice.get_status_display()}

*Payment Method:* {invoice.payment.get_payment_method_display()}
*Invoice Date:* {invoice.issue_date.strftime('%B %d, %Y')}

Your fish delivery is being prepared. You'll receive updates on the delivery progress.

Thank you for choosing Fish Auction App! 🐟
                """.strip()

                try:
                    notification_service.send_whatsapp_message(
                        invoice.payment.buyer,
                        buyer_message
                    )

                    # Send invoice PDF if available
                    if invoice_url:
                        notification_service.send_whatsapp_document(
                            invoice.payment.buyer,
                            invoice_url,
                            f"Invoice #{invoice.invoice_number}"
                        )

                except Exception as e:
                    logger.error(f"Failed to send WhatsApp to buyer {invoice.payment.buyer.id}: {str(e)}")

            # Send to seller
            if invoice.payment.seller.phone_number:
                seller_message = f"""
💰 *Payment Received - Invoice Generated*

Hello {invoice.seller_name},

Great news! Payment has been received for your auction.

*Sale Details:*
• Item: {invoice.payment.auction.title}
• Buyer: {invoice.buyer_name}
• Amount: ${invoice.payment.seller_amount:.2f} (after platform fee)
• Invoice #: {invoice.invoice_number}

*Next Steps:*
• Prepare the fish for delivery
• Update delivery status in the app
• Funds have been added to your wallet

Invoice attached for your records.

Thank you for selling with Fish Auction App! 🐟
                """.strip()

                try:
                    notification_service.send_whatsapp_message(
                        invoice.payment.seller,
                        seller_message
                    )

                    # Send invoice PDF if available
                    if invoice_url:
                        notification_service.send_whatsapp_document(
                            invoice.payment.seller,
                            invoice_url,
                            f"Invoice #{invoice.invoice_number}"
                        )

                except Exception as e:
                    logger.error(f"Failed to send WhatsApp to seller {invoice.payment.seller.id}: {str(e)}")

            logger.info(f"WhatsApp invoice notifications sent for invoice {invoice.invoice_number}")

        except Exception as e:
            logger.error(f"Failed to send WhatsApp notifications for invoice {invoice.id}: {str(e)}")
            # Don't raise exception to avoid breaking payment flow
    
    def process_refund(self, payment, amount, reason, requested_by):
        """Process a refund"""
        try:
            refund = Refund.objects.create(
                payment=payment,
                amount=amount,
                reason=reason,
                requested_by=requested_by,
                status='processing'
            )
            
            if payment.payment_method == 'wallet':
                # Refund to wallet
                self.add_wallet_funds(
                    payment.buyer, 
                    amount, 
                    f"Refund for auction: {payment.auction.title}"
                )
                refund.status = 'completed'
                refund.processed_at = timezone.now()
                
            elif payment.payment_method == 'stripe' and payment.stripe_charge_id:
                # Refund via Stripe
                stripe_refund = stripe.Refund.create(
                    charge=payment.stripe_charge_id,
                    amount=int(amount * 100),  # Convert to cents
                    metadata={
                        'refund_id': str(refund.id),
                        'payment_id': str(payment.id)
                    }
                )
                
                refund.stripe_refund_id = stripe_refund.id
                refund.status = 'completed'
                refund.processed_at = timezone.now()
            
            refund.save()
            return refund
            
        except Exception as e:
            logger.error(f"Failed to process refund for payment {payment.id}: {str(e)}")
            if 'refund' in locals():
                refund.status = 'failed'
                refund.save()
            raise

    def create_delivery_tracking(self, payment):
        """Create delivery tracking for completed payment"""
        try:
            from delivery.models import Delivery
            from datetime import timedelta

            # Check if delivery already exists
            if hasattr(payment, 'delivery'):
                logger.info(f"Delivery already exists for payment {payment.id}")
                return payment.delivery

            # Create delivery tracking
            delivery = Delivery.objects.create(
                auction=payment.auction,
                payment=payment,
                seller=payment.seller,
                buyer=payment.buyer,
                pickup_address=f"Seller location: {payment.auction.catch_location}",
                delivery_address="Buyer address (to be updated)",
                status='pending',
                estimated_pickup_time=timezone.now() + timedelta(hours=2),
                estimated_delivery_time=timezone.now() + timedelta(days=1),
                delivery_cost=0,  # Can be updated later
                special_instructions=f"Delivery for auction: {payment.auction.title}"
            )

            logger.info(f"Created delivery tracking {delivery.tracking_number} for payment {payment.id}")
            return delivery

        except Exception as e:
            logger.error(f"Failed to create delivery tracking for payment {payment.id}: {str(e)}")
            # Don't raise exception as delivery creation shouldn't fail payment
            return None


class WalletService:
    """Service for wallet operations"""

    def get_wallet_balance(self, user):
        """Get user's current wallet balance"""
        return user.wallet_balance

    def get_wallet_transactions(self, user, limit=50):
        """Get user's wallet transaction history"""
        return WalletTransaction.objects.filter(user=user)[:limit]

    def create_stripe_payment_intent_for_wallet_topup(self, user, amount):
        """Create Stripe payment intent for wallet top-up"""
        try:
            intent = stripe.PaymentIntent.create(
                amount=int(amount * 100),  # Convert to cents
                currency='usd',
                metadata={
                    'user_id': str(user.id),
                    'type': 'wallet_topup'
                }
            )

            return intent

        except Exception as e:
            logger.error(f"Failed to create payment intent for wallet top-up: {str(e)}")
            raise
