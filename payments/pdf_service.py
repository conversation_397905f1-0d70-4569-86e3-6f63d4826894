import os
import io
from django.conf import settings
from django.core.files.base import ContentFile
from django.template.loader import render_to_string
from django.utils import timezone
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
import logging

logger = logging.getLogger(__name__)


class InvoicePDFService:
    """Service for generating PDF invoices"""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
        self.labels = self._get_localized_labels()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles"""
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2E86AB')
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.HexColor('#2E86AB')
        ))
        
        self.styles.add(ParagraphStyle(
            name='RightAlign',
            parent=self.styles['Normal'],
            alignment=TA_RIGHT,
        ))

    def _get_localized_labels(self, language='en'):
        """Get localized labels for PDF generation"""
        labels = {
            'en': {
                'invoice': 'INVOICE',
                'invoice_date': 'Invoice Date:',
                'due_date': 'Due Date:',
                'paid_date': 'Paid Date:',
                'status': 'Status:',
                'bill_to': 'BILL TO:',
                'bill_from': 'BILL FROM:',
                'auction_details': 'Auction Details',
                'item': 'Item',
                'description': 'Description',
                'quantity': 'Quantity',
                'price': 'Price',
                'subtotal': 'Subtotal:',
                'platform_fee': 'Platform Fee:',
                'tax': 'Tax:',
                'total': 'TOTAL:',
                'thank_you': 'Thank you for your business!',
                'footer_text': 'Fish Auction App - Connecting Fishermen with Buyers',
                'support_text': 'For support, contact <NAME_EMAIL>',
                'draft': 'Draft',
                'sent': 'Sent',
                'paid': 'Paid',
                'overdue': 'Overdue',
                'cancelled': 'Cancelled',
            },
            'ar': {
                'invoice': 'فاتورة',
                'invoice_date': 'تاريخ الفاتورة:',
                'due_date': 'تاريخ الاستحقاق:',
                'paid_date': 'تاريخ الدفع:',
                'status': 'الحالة:',
                'bill_to': 'فاتورة إلى:',
                'bill_from': 'فاتورة من:',
                'auction_details': 'تفاصيل المزاد',
                'item': 'العنصر',
                'description': 'الوصف',
                'quantity': 'الكمية',
                'price': 'السعر',
                'subtotal': 'المجموع الفرعي:',
                'platform_fee': 'رسوم المنصة:',
                'tax': 'الضريبة:',
                'total': 'المجموع الإجمالي:',
                'thank_you': 'شكراً لك على تعاملك معنا!',
                'footer_text': 'تطبيق مزاد الأسماك - ربط الصيادين بالمشترين',
                'support_text': 'للدعم، تواصل معنا على <EMAIL>',
                'draft': 'مسودة',
                'sent': 'مرسل',
                'paid': 'مدفوع',
                'overdue': 'متأخر',
                'cancelled': 'ملغي',
            }
        }
        return labels.get(language, labels['en'])
    
    def generate_invoice_pdf(self, invoice, language=None):
        """Generate PDF for an invoice"""
        try:
            # Use invoice language if not specified
            if language is None:
                language = getattr(invoice, 'language', 'en')

            # Update labels for the specified language
            self.labels = self._get_localized_labels(language)
            # Create a BytesIO buffer to hold the PDF
            buffer = io.BytesIO()
            
            # Create the PDF document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build the PDF content
            story = []
            
            # Add header
            self._add_header(story, invoice)
            
            # Add invoice details
            self._add_invoice_details(story, invoice)
            
            # Add billing information
            self._add_billing_info(story, invoice)
            
            # Add auction details
            self._add_auction_details(story, invoice)
            
            # Add payment summary
            self._add_payment_summary(story, invoice)
            
            # Add footer
            self._add_footer(story, invoice)
            
            # Build the PDF
            doc.build(story)
            
            # Get the PDF content
            pdf_content = buffer.getvalue()
            buffer.close()
            
            return pdf_content
            
        except Exception as e:
            logger.error(f"Failed to generate PDF for invoice {invoice.id}: {str(e)}")
            raise
    
    def _add_header(self, story, invoice):
        """Add header section to the PDF"""
        # Company logo and title
        title = Paragraph("Fish Auction App", self.styles['CustomTitle'])
        story.append(title)
        story.append(Spacer(1, 20))

        # Invoice title and number
        invoice_title = Paragraph(f"{self.labels['invoice']} #{invoice.invoice_number}", self.styles['CustomHeading'])
        story.append(invoice_title)
        story.append(Spacer(1, 20))
    
    def _add_invoice_details(self, story, invoice):
        """Add invoice details section"""
        # Get localized status
        status_text = self.labels.get(invoice.status, invoice.get_status_display())

        # Create table for invoice details
        data = [
            [self.labels['invoice_date'], invoice.issue_date.strftime('%B %d, %Y')],
            [self.labels['due_date'], invoice.due_date.strftime('%B %d, %Y')],
            [self.labels['status'], status_text],
        ]

        if invoice.paid_date:
            data.append([self.labels['paid_date'], invoice.paid_date.strftime('%B %d, %Y')])
        
        table = Table(data, colWidths=[2*inch, 3*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_billing_info(self, story, invoice):
        """Add billing information section"""
        # Create table for billing info
        data = [
            [self.labels['bill_to'], self.labels['bill_from']],
            [
                f"{invoice.buyer_name}\n{invoice.buyer_email}\n{invoice.buyer_address}",
                f"{invoice.seller_name}\n{invoice.seller_email}\n{invoice.seller_address}"
            ]
        ]
        
        table = Table(data, colWidths=[3*inch, 3*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_auction_details(self, story, invoice):
        """Add auction details section"""
        auction = invoice.payment.auction
        
        # Auction details header
        auction_header = Paragraph(self.labels['auction_details'], self.styles['CustomHeading'])
        story.append(auction_header)

        # Auction details table
        data = [
            [self.labels['item'], self.labels['description'], self.labels['quantity'], self.labels['price']],
            [
                auction.title,
                f"{auction.fish_category.name if auction.fish_category else 'N/A'} - {auction.fish_type or 'N/A'}\n"
                f"Weight: {auction.weight}kg\n"
                f"Catch Date: {auction.catch_date.strftime('%Y-%m-%d') if auction.catch_date else 'N/A'}\n"
                f"Location: {auction.catch_location or 'N/A'}",
                f"{auction.quantity}",
                f"${invoice.payment.amount:.2f}"
            ]
        ]
        
        table = Table(data, colWidths=[2*inch, 3*inch, 1*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (2, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_payment_summary(self, story, invoice):
        """Add payment summary section"""
        # Payment summary data
        data = [
            [self.labels['subtotal'], f"${invoice.subtotal:.2f}"],
            [self.labels['platform_fee'], f"${invoice.payment.platform_fee:.2f}"],
            [self.labels['tax'], f"${invoice.tax_amount:.2f}"],
            [self.labels['total'], f"${invoice.total_amount:.2f}"]
        ]
        
        table = Table(data, colWidths=[4*inch, 2*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#F0F0F0')),
            ('LINEBELOW', (0, -2), (-1, -2), 1, colors.black),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 30))
    
    def _add_footer(self, story, invoice):
        """Add footer section"""
        footer_text = f"""
        <para align=center>
        <b>{self.labels['thank_you']}</b><br/>
        {self.labels['footer_text']}<br/>
        {self.labels['support_text']}
        </para>
        """
        
        footer = Paragraph(footer_text, self.styles['Normal'])
        story.append(footer)
    
    def save_invoice_pdf(self, invoice, language=None):
        """Generate and save PDF file for an invoice"""
        try:
            # Generate PDF content
            pdf_content = self.generate_invoice_pdf(invoice, language)
            
            # Create filename
            filename = f"invoice_{invoice.invoice_number}.pdf"
            
            # Save PDF file
            invoice.pdf_file.save(
                filename,
                ContentFile(pdf_content),
                save=True
            )
            
            logger.info(f"PDF generated and saved for invoice {invoice.invoice_number}")
            return invoice.pdf_file.url
            
        except Exception as e:
            logger.error(f"Failed to save PDF for invoice {invoice.id}: {str(e)}")
            raise
