import os
import io
from django.conf import settings
from django.core.files.base import ContentFile
from django.template.loader import render_to_string
from django.utils import timezone
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
import logging

logger = logging.getLogger(__name__)


class InvoicePDFService:
    """Service for generating PDF invoices"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles"""
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2E86AB')
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.HexColor('#2E86AB')
        ))
        
        self.styles.add(ParagraphStyle(
            name='RightAlign',
            parent=self.styles['Normal'],
            alignment=TA_RIGHT,
        ))
    
    def generate_invoice_pdf(self, invoice):
        """Generate PDF for an invoice"""
        try:
            # Create a BytesIO buffer to hold the PDF
            buffer = io.BytesIO()
            
            # Create the PDF document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build the PDF content
            story = []
            
            # Add header
            self._add_header(story, invoice)
            
            # Add invoice details
            self._add_invoice_details(story, invoice)
            
            # Add billing information
            self._add_billing_info(story, invoice)
            
            # Add auction details
            self._add_auction_details(story, invoice)
            
            # Add payment summary
            self._add_payment_summary(story, invoice)
            
            # Add footer
            self._add_footer(story, invoice)
            
            # Build the PDF
            doc.build(story)
            
            # Get the PDF content
            pdf_content = buffer.getvalue()
            buffer.close()
            
            return pdf_content
            
        except Exception as e:
            logger.error(f"Failed to generate PDF for invoice {invoice.id}: {str(e)}")
            raise
    
    def _add_header(self, story, invoice):
        """Add header section to the PDF"""
        # Company logo and title
        title = Paragraph("Fish Auction App", self.styles['CustomTitle'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Invoice title and number
        invoice_title = Paragraph(f"INVOICE #{invoice.invoice_number}", self.styles['CustomHeading'])
        story.append(invoice_title)
        story.append(Spacer(1, 20))
    
    def _add_invoice_details(self, story, invoice):
        """Add invoice details section"""
        # Create table for invoice details
        data = [
            ['Invoice Date:', invoice.issue_date.strftime('%B %d, %Y')],
            ['Due Date:', invoice.due_date.strftime('%B %d, %Y')],
            ['Status:', invoice.get_status_display()],
        ]
        
        if invoice.paid_date:
            data.append(['Paid Date:', invoice.paid_date.strftime('%B %d, %Y')])
        
        table = Table(data, colWidths=[2*inch, 3*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_billing_info(self, story, invoice):
        """Add billing information section"""
        # Create table for billing info
        data = [
            ['BILL TO:', 'BILL FROM:'],
            [
                f"{invoice.buyer_name}\n{invoice.buyer_email}\n{invoice.buyer_address}",
                f"{invoice.seller_name}\n{invoice.seller_email}\n{invoice.seller_address}"
            ]
        ]
        
        table = Table(data, colWidths=[3*inch, 3*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_auction_details(self, story, invoice):
        """Add auction details section"""
        auction = invoice.payment.auction
        
        # Auction details header
        auction_header = Paragraph("Auction Details", self.styles['CustomHeading'])
        story.append(auction_header)
        
        # Auction details table
        data = [
            ['Item', 'Description', 'Quantity', 'Price'],
            [
                auction.title,
                f"{auction.fish_category.name if auction.fish_category else 'N/A'} - {auction.fish_type or 'N/A'}\n"
                f"Weight: {auction.weight}kg\n"
                f"Catch Date: {auction.catch_date.strftime('%Y-%m-%d') if auction.catch_date else 'N/A'}\n"
                f"Location: {auction.catch_location or 'N/A'}",
                f"{auction.quantity}",
                f"${invoice.payment.amount:.2f}"
            ]
        ]
        
        table = Table(data, colWidths=[2*inch, 3*inch, 1*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (2, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_payment_summary(self, story, invoice):
        """Add payment summary section"""
        # Payment summary data
        data = [
            ['Subtotal:', f"${invoice.subtotal:.2f}"],
            ['Platform Fee:', f"${invoice.payment.platform_fee:.2f}"],
            ['Tax:', f"${invoice.tax_amount:.2f}"],
            ['TOTAL:', f"${invoice.total_amount:.2f}"]
        ]
        
        table = Table(data, colWidths=[4*inch, 2*inch])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#F0F0F0')),
            ('LINEBELOW', (0, -2), (-1, -2), 1, colors.black),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 30))
    
    def _add_footer(self, story, invoice):
        """Add footer section"""
        footer_text = """
        <para align=center>
        <b>Thank you for your business!</b><br/>
        Fish Auction App - Connecting Fishermen with Buyers<br/>
        For support, contact <NAME_EMAIL>
        </para>
        """
        
        footer = Paragraph(footer_text, self.styles['Normal'])
        story.append(footer)
    
    def save_invoice_pdf(self, invoice):
        """Generate and save PDF file for an invoice"""
        try:
            # Generate PDF content
            pdf_content = self.generate_invoice_pdf(invoice)
            
            # Create filename
            filename = f"invoice_{invoice.invoice_number}.pdf"
            
            # Save PDF file
            invoice.pdf_file.save(
                filename,
                ContentFile(pdf_content),
                save=True
            )
            
            logger.info(f"PDF generated and saved for invoice {invoice.invoice_number}")
            return invoice.pdf_file.url
            
        except Exception as e:
            logger.error(f"Failed to save PDF for invoice {invoice.id}: {str(e)}")
            raise
