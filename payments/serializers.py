from rest_framework import serializers
from .models import Payment, WalletTransaction, Invoice, Refund, PayoutMethod, WithdrawalRequest
from auctions.serializers import AuctionListSerializer


class WalletTransactionSerializer(serializers.ModelSerializer):
    """Serializer for wallet transactions"""
    
    auction = AuctionListSerializer(read_only=True)
    
    class Meta:
        model = WalletTransaction
        fields = [
            'id', 'transaction_type', 'amount', 'status', 'description',
            'auction', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class PaymentSerializer(serializers.ModelSerializer):
    """Serializer for payments"""
    
    auction = AuctionListSerializer(read_only=True)
    buyer_name = serializers.CharField(source='buyer.get_full_name', read_only=True)
    seller_name = serializers.CharField(source='seller.get_full_name', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'auction', 'buyer_name', 'seller_name', 'amount',
            'platform_fee', 'seller_amount', 'payment_method', 'status',
            'payment_deadline', 'paid_at', 'created_at', 'is_overdue'
        ]
        read_only_fields = [
            'id', 'auction', 'buyer_name', 'seller_name', 'amount',
            'platform_fee', 'seller_amount', 'paid_at', 'created_at', 'is_overdue'
        ]


class InvoiceSerializer(serializers.ModelSerializer):
    """Serializer for invoices"""
    
    payment = PaymentSerializer(read_only=True)
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'payment', 'invoice_number', 'buyer_name', 'buyer_email',
            'buyer_address', 'seller_name', 'seller_email', 'seller_address',
            'subtotal', 'tax_amount', 'total_amount', 'status',
            'issue_date', 'due_date', 'paid_date'
        ]
        read_only_fields = [
            'id', 'invoice_number', 'issue_date', 'paid_date'
        ]


class RefundSerializer(serializers.ModelSerializer):
    """Serializer for refunds"""
    
    payment = PaymentSerializer(read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.get_full_name', read_only=True)
    processed_by_name = serializers.CharField(source='processed_by.get_full_name', read_only=True)
    
    class Meta:
        model = Refund
        fields = [
            'id', 'payment', 'amount', 'reason', 'description', 'status',
            'requested_by_name', 'processed_by_name', 'created_at', 'processed_at'
        ]
        read_only_fields = [
            'id', 'payment', 'requested_by_name', 'processed_by_name',
            'created_at', 'processed_at'
        ]


class WalletTopUpSerializer(serializers.Serializer):
    """Serializer for wallet top-up"""
    
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=1)
    payment_method_id = serializers.CharField(required=False)
    
    def validate_amount(self, value):
        if value < 1:
            raise serializers.ValidationError("Minimum top-up amount is $1")
        if value > 10000:
            raise serializers.ValidationError("Maximum top-up amount is $10,000")
        return value


class ProcessPaymentSerializer(serializers.Serializer):
    """Serializer for processing payments"""
    
    payment_method = serializers.ChoiceField(choices=['wallet', 'stripe'])
    payment_method_id = serializers.CharField(required=False)
    
    def validate(self, attrs):
        if attrs['payment_method'] == 'stripe' and not attrs.get('payment_method_id'):
            raise serializers.ValidationError("payment_method_id is required for Stripe payments")
        return attrs


class RefundRequestSerializer(serializers.Serializer):
    """Serializer for refund requests"""
    
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    reason = serializers.ChoiceField(choices=Refund.REASON_CHOICES)
    description = serializers.CharField(max_length=500, required=False)
    
    def validate_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Refund amount must be positive")
        return value


class PayoutMethodSerializer(serializers.ModelSerializer):
    """Serializer for payout methods"""

    class Meta:
        model = PayoutMethod
        fields = [
            'id', 'payout_type', 'is_default', 'is_verified',
            'paypal_email', 'stripe_account_id', 'bank_name',
            'account_holder_name', 'account_number', 'routing_number',
            'iban', 'swift_code', 'payoneer_email', 'created_at'
        ]
        read_only_fields = ['id', 'is_verified', 'created_at']

    def validate(self, attrs):
        payout_type = attrs.get('payout_type')

        if payout_type == 'paypal' and not attrs.get('paypal_email'):
            raise serializers.ValidationError("PayPal email is required for PayPal payout method")

        if payout_type == 'stripe' and not attrs.get('stripe_account_id'):
            raise serializers.ValidationError("Stripe account ID is required for Stripe payout method")

        if payout_type == 'bank_transfer':
            required_fields = ['bank_name', 'account_holder_name', 'account_number']
            for field in required_fields:
                if not attrs.get(field):
                    raise serializers.ValidationError(f"{field} is required for bank transfer")

        if payout_type == 'payoneer' and not attrs.get('payoneer_email'):
            raise serializers.ValidationError("Payoneer email is required for Payoneer payout method")

        return attrs


class WithdrawalRequestSerializer(serializers.ModelSerializer):
    """Serializer for withdrawal requests"""

    payout_method_details = PayoutMethodSerializer(source='payout_method', read_only=True)

    class Meta:
        model = WithdrawalRequest
        fields = [
            'id', 'amount', 'platform_fee', 'payout_amount', 'status',
            'admin_notes', 'external_transaction_id', 'created_at',
            'updated_at', 'approved_at', 'completed_at', 'payout_method_details'
        ]
        read_only_fields = [
            'id', 'platform_fee', 'payout_amount', 'status', 'admin_notes',
            'external_transaction_id', 'created_at', 'updated_at',
            'approved_at', 'completed_at'
        ]


class WithdrawalRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating withdrawal requests"""

    class Meta:
        model = WithdrawalRequest
        fields = ['amount', 'payout_method']

    def validate_amount(self, value):
        if value < 10:
            raise serializers.ValidationError("Minimum withdrawal amount is $10")
        if value > 50000:
            raise serializers.ValidationError("Maximum withdrawal amount is $50,000")
        return value
