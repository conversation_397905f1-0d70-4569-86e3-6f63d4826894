import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from .models import Auction, Bid, AutoBid
from accounts.models import User


class AuctionConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time auction updates"""
    
    async def connect(self):
        self.auction_id = self.scope['url_route']['kwargs']['auction_id']
        self.auction_group_name = f'auction_{self.auction_id}'

        print(f"🔌 WebSocket connection attempt for auction {self.auction_id}")

        # Join auction group
        await self.channel_layer.group_add(
            self.auction_group_name,
            self.channel_name
        )

        await self.accept()
        print(f"✅ WebSocket connection accepted for auction {self.auction_id}")

        # Send current auction status
        auction_data = await self.get_auction_data()
        if auction_data:
            await self.send(text_data=json.dumps({
                'type': 'auction_status',
                'data': auction_data
            }))
            print(f"📤 Sent auction status for auction {self.auction_id}")
    
    async def disconnect(self, close_code):
        # Leave auction group
        await self.channel_layer.group_discard(
            self.auction_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'place_bid':
                await self.handle_bid(text_data_json)
            elif message_type == 'join_auction':
                await self.handle_join_auction(text_data_json)
            elif message_type == 'update_location':
                await self.handle_location_update(text_data_json)
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
    
    async def handle_bid(self, data):
        """Handle bid placement"""
        user = self.scope.get('user')
        print(f"🔍 Bid attempt by user: {user}")

        if isinstance(user, AnonymousUser):
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Authentication required - please login'
            }))
            return
        
        bid_amount = data.get('amount')
        if not bid_amount:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Bid amount is required'
            }))
            return
        
        # Process bid (this would typically be handled by a service)
        bid_result = await self.process_bid(user, bid_amount)
        
        if bid_result['success']:
            # Broadcast bid to all auction participants
            await self.channel_layer.group_send(
                self.auction_group_name,
                {
                    'type': 'bid_update',
                    'bid_data': bid_result['bid_data']
                }
            )
        else:
            await self.send(text_data=json.dumps({
                'type': 'bid_error',
                'message': bid_result['error']
            }))
    
    async def handle_join_auction(self, data):
        """Handle user joining auction"""
        user = self.scope.get('user')
        if not isinstance(user, AnonymousUser):
            await self.channel_layer.group_send(
                self.auction_group_name,
                {
                    'type': 'user_joined',
                    'user_data': {
                        'username': user.username,
                        'user_type': user.user_type
                    }
                }
            )

    async def handle_location_update(self, data):
        """Handle seller location update"""
        try:
            user = self.scope.get('user')
            if not user or isinstance(user, AnonymousUser):
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Authentication required for location updates'
                }))
                return

            if user.user_type not in ['seller', 'admin']:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Only sellers can update location'
                }))
                return

            # Validate required fields
            latitude = data.get('latitude')
            longitude = data.get('longitude')

            if not latitude or not longitude:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Latitude and longitude are required'
                }))
                return

            # Update location in database
            location_data = await self.update_seller_location(
                user, latitude, longitude, data
            )

            if location_data:
                # Broadcast location update to all auction participants
                await self.channel_layer.group_send(
                    self.auction_group_name,
                    {
                        'type': 'location_update',
                        'location_data': location_data
                    }
                )

                await self.send(text_data=json.dumps({
                    'type': 'location_update_success',
                    'message': 'Location updated successfully'
                }))
            else:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Failed to update location'
                }))

        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Location update failed: {str(e)}'
            }))
    
    # WebSocket message handlers
    async def bid_update(self, event):
        """Send bid update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'bid_update',
            'data': event['bid_data']
        }))
    
    async def auction_status(self, event):
        """Send auction status update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'auction_status',
            'data': event['auction_data']
        }))
    
    async def user_joined(self, event):
        """Send user joined notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'user_joined',
            'data': event['user_data']
        }))
    
    async def auction_ended(self, event):
        """Send auction ended notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'auction_ended',
            'data': event['auction_data']
        }))

    async def location_update(self, event):
        """Send location update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'location_update',
            'data': event['location_data']
        }))
    
    @database_sync_to_async
    def get_auction_data(self):
        """Get current auction data"""
        try:
            auction = Auction.objects.get(id=self.auction_id)
            latest_bid = auction.bids.first()
            
            return {
                'auction_id': auction.id,
                'title': auction.title,
                'current_price': str(auction.current_price),
                'target_price': str(auction.target_price) if auction.target_price else None,
                'status': auction.status,
                'time_remaining': str(auction.time_remaining),
                'total_bids': auction.total_bids,
                'latest_bid': {
                    'amount': str(latest_bid.amount),
                    'bidder': latest_bid.bidder.username,
                    'timestamp': latest_bid.timestamp.isoformat()
                } if latest_bid else None
            }
        except Auction.DoesNotExist:
            return None
    
    @database_sync_to_async
    def process_bid(self, user, bid_amount):
        """Process a bid and trigger auto-bids"""
        try:
            from django.db import transaction
            from decimal import Decimal

            auction = Auction.objects.get(id=self.auction_id)

            # Basic validation
            if auction.status != 'live':
                return {'success': False, 'error': 'Auction is not live'}

            if float(bid_amount) <= float(auction.current_price):
                return {'success': False, 'error': 'Bid must be higher than current price'}

            with transaction.atomic():
                # Create manual bid
                bid = Bid.objects.create(
                    auction=auction,
                    bidder=user,
                    amount=bid_amount,
                    bid_type='manual'
                )

                # Update auction
                auction.current_price = bid_amount
                auction.total_bids += 1
                auction.save()

                # Trigger auto-bids via background task (works even when user offline)
                from .tasks import process_auto_bids_for_auction
                process_auto_bids_for_auction.delay(auction.id, bid.id)

            return {
                'success': True,
                'bid_data': {
                    'auction_id': auction.id,
                    'bid_id': bid.id,
                    'amount': str(bid.amount),
                    'bidder': bid.bidder.username,
                    'timestamp': bid.timestamp.isoformat(),
                    'current_price': str(auction.current_price),
                    'total_bids': auction.total_bids,
                    'bid_type': 'manual'
                }
            }

        except Auction.DoesNotExist:
            return {'success': False, 'error': 'Auction not found'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def trigger_auto_bids(self, auction, manual_bidder):
        """Trigger auto-bids for other users - continues until no more auto-bids possible"""
        from decimal import Decimal
        import time

        max_iterations = 20  # Prevent infinite loops
        iteration = 0

        while iteration < max_iterations:
            iteration += 1

            # Get the last bidder (could be manual or auto)
            last_bid = Bid.objects.filter(auction=auction).order_by('-timestamp').first()
            last_bidder = last_bid.bidder if last_bid else manual_bidder

            # Get active auto-bids for this auction (excluding the last bidder)
            next_bid_amount = auction.current_price + auction.bid_increment
            auto_bids = AutoBid.objects.filter(
                auction=auction,
                is_active=True,
                max_amount__gte=next_bid_amount  # Fixed: use next_bid_amount instead of current_price
            ).exclude(bidder=last_bidder).order_by('-max_amount')

            if not auto_bids.exists():
                print(f"🏁 No more auto-bids available after {iteration-1} iterations")
                break

            # Get the highest auto-bid that can still bid
            auto_bid = auto_bids.first()
            # next_bid_amount already calculated above for the filter

            # This condition should always be true now due to our filter, but keep for safety
            if next_bid_amount <= auto_bid.max_amount:
                # Place auto-bid
                auto_bid_obj = Bid.objects.create(
                    auction=auction,
                    bidder=auto_bid.bidder,
                    amount=next_bid_amount,
                    bid_type='auto'
                )

                # Update auction
                auction.current_price = next_bid_amount
                auction.total_bids += 1
                auction.save()

                # Update auto-bid current amount
                auto_bid.current_bid_amount = next_bid_amount
                auto_bid.save()

                # Send real-time update for auto-bid
                from asgiref.sync import async_to_sync
                async_to_sync(self.channel_layer.group_send)(
                    f'auction_{auction.id}',
                    {
                        'type': 'bid_update',
                        'bid_data': {
                            'auction_id': auction.id,
                            'bid_id': auto_bid_obj.id,
                            'amount': str(auto_bid_obj.amount),
                            'bidder': auto_bid_obj.bidder.username,
                            'timestamp': auto_bid_obj.timestamp.isoformat(),
                            'current_price': str(auction.current_price),
                            'total_bids': auction.total_bids,
                            'bid_type': 'auto'
                        }
                    }
                )

                print(f"🤖 Auto-bid #{iteration}: ${next_bid_amount} by {auto_bid.bidder.username}")

                # Small delay to prevent race conditions and allow WebSocket updates
                time.sleep(0.2)

                # Continue to next iteration to check for more auto-bids
                continue
            else:
                # Max amount reached, deactivate auto-bid
                auto_bid.is_active = False
                auto_bid.save()
                print(f"🚫 Auto-bid max reached for {auto_bid.bidder.username}")

                # Continue to check other auto-bids
                continue

    @database_sync_to_async
    def update_seller_location(self, user, latitude, longitude, data):
        """Update seller location in database"""
        try:
            from django.utils import timezone
            from .models import SellerLocation

            # Create new location record
            location = SellerLocation.objects.create(
                seller=user,
                auction_id=self.auction_id,
                latitude=latitude,
                longitude=longitude,
                accuracy=data.get('accuracy'),
                altitude=data.get('altitude'),
                heading=data.get('heading'),
                speed=data.get('speed'),
                status=data.get('status', 'online'),
                address=data.get('address', ''),
                is_active=True
            )

            # Update auction location
            try:
                auction = Auction.objects.get(id=self.auction_id, seller=user)
                auction.latitude = latitude
                auction.longitude = longitude
                auction.location_updated_at = timezone.now()
                auction.is_location_live = True
                auction.save(update_fields=['latitude', 'longitude', 'location_updated_at', 'is_location_live'])
            except Auction.DoesNotExist:
                pass

            return {
                'auction_id': self.auction_id,
                'latitude': str(latitude),
                'longitude': str(longitude),
                'status': location.status,
                'address': location.address,
                'timestamp': location.timestamp.isoformat(),
                'seller_name': user.username,
                'accuracy': location.accuracy,
                'altitude': location.altitude,
                'heading': location.heading,
                'speed': location.speed
            }

        except Exception as e:
            print(f"Error updating seller location: {e}")
            return None

    @database_sync_to_async
    def get_test_user(self):
        """Get a test user for anonymous connections (testing only)"""
        try:
            # Get the first available user for testing
            return User.objects.filter(user_type='buyer').first()
        except:
            return None
