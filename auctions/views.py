from rest_framework import generics, status, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, OpenApiParameter
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import Auction, FishCategory, Bid, AutoBid, AuctionWatchlist, SellerLocation
from .serializers import (
    AuctionListSerializer, AuctionDetailSerializer, AuctionCreateSerializer,
    FishCategorySerializer, BidSerializer, AutoBidSerializer,
    PlaceBidSerializer, AuctionWatchlistSerializer, SellerLocationSerializer,
    UpdateSellerLocationSerializer, AuctionLocationSerializer
)


class FishCategoryListView(generics.ListAPIView):
    """List all fish categories"""

    queryset = FishCategory.objects.filter(is_active=True)
    serializer_class = FishCategorySerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="List fish categories",
        description="Get all active fish categories"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class AuctionListView(generics.ListAPIView):
    """List auctions with filtering and search"""

    queryset = Auction.objects.all()
    serializer_class = AuctionListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'auction_type', 'fish_category', 'seller']
    search_fields = ['title', 'description', 'fish_type', 'catch_location']
    ordering_fields = ['created_at', 'start_time', 'current_price', 'total_bids']
    ordering = ['-created_at']

    @extend_schema(
        summary="List auctions",
        description="Get list of auctions with filtering and search capabilities",
        parameters=[
            OpenApiParameter(name='status', description='Filter by auction status'),
            OpenApiParameter(name='auction_type', description='Filter by auction type'),
            OpenApiParameter(name='fish_category', description='Filter by fish category'),
            OpenApiParameter(name='search', description='Search in title, description, fish type, location'),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class AuctionDetailView(generics.RetrieveAPIView):
    """Get auction details"""

    queryset = Auction.objects.all()
    serializer_class = AuctionDetailSerializer
    permission_classes = [permissions.AllowAny]

    def get_object(self):
        obj = super().get_object()
        # Increment view count
        obj.views_count += 1
        obj.save(update_fields=['views_count'])
        return obj

    @extend_schema(
        summary="Get auction details",
        description="Get detailed information about a specific auction"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class AuctionCreateView(generics.CreateAPIView):
    """Create new auction"""

    queryset = Auction.objects.all()
    serializer_class = AuctionCreateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        # Allow setting status from request data, default to draft
        status = self.request.data.get('status', 'draft')
        serializer.save(seller=self.request.user, status=status)

    @extend_schema(
        summary="Create auction",
        description="Create a new auction (sellers only)"
    )
    def post(self, request, *args, **kwargs):
        if request.user.user_type not in ['seller', 'admin']:
            return Response(
                {'error': 'Only sellers can create auctions'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if seller is verified (admin users bypass this check)
        if request.user.user_type == 'seller' and not request.user.is_verified:
            return Response(
                {'error': 'You must be verified to create auctions. Please complete your document verification first.'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().post(request, *args, **kwargs)


class PlaceBidView(APIView):
    """Place a bid on an auction"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Place bid",
        description="Place a bid on an auction",
        request=PlaceBidSerializer
    )
    def post(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)

        if request.user.user_type not in ['buyer', 'admin']:
            return Response(
                {'error': 'Only buyers can place bids'},
                status=status.HTTP_403_FORBIDDEN
            )

        if auction.seller == request.user:
            return Response(
                {'error': 'Cannot bid on your own auction'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = PlaceBidSerializer(data=request.data, context={'auction': auction})
        if serializer.is_valid():
            from django.db import transaction
            from decimal import Decimal

            with transaction.atomic():
                # Create manual bid
                bid = Bid.objects.create(
                    auction=auction,
                    bidder=request.user,
                    amount=serializer.validated_data['amount'],
                    bid_type='manual'
                )

                # Update auction
                auction.current_price = bid.amount
                auction.total_bids += 1
                auction.save()

                # Send real-time update for manual bid
                try:
                    channel_layer = get_channel_layer()
                    if channel_layer:
                        async_to_sync(channel_layer.group_send)(
                            f'auction_{auction.id}',
                            {
                                'type': 'bid_update',
                                'bid_data': {
                                    'auction_id': auction.id,
                                    'bid_id': bid.id,
                                    'amount': str(bid.amount),
                                    'bidder': bid.bidder.username,
                                    'timestamp': bid.timestamp.isoformat(),
                                    'current_price': str(auction.current_price),
                                    'total_bids': auction.total_bids,
                                    'bid_type': 'manual'
                                }
                            }
                        )
                except Exception as e:
                    # Log error but don't fail the request
                    logger.warning(f"Failed to send real-time update: {str(e)}")

                # Trigger auto-bids via background task (async processing only)
                from .tasks import process_auto_bids_for_auction
                process_auto_bids_for_auction.delay(auction.id, bid.id)

            return Response(BidSerializer(bid).data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def close_auction_early(request, auction_id):
    """Close auction early when target price is reached"""
    try:
        auction = get_object_or_404(Auction, id=auction_id)

        # Check if user is the seller
        if auction.seller != request.user:
            return Response(
                {'error': 'Only the seller can close the auction early'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if auction can be closed early
        if not auction.can_close_early():
            return Response(
                {'error': 'Auction cannot be closed early. Target price not reached or auction not live.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Close the auction
        auction.status = 'ended'
        auction.end_time = timezone.now()

        # Set winner if there are bids (highest bidder wins)
        winning_bid = auction.bids.order_by('-amount').first()
        if winning_bid:
            auction.winner = winning_bid.bidder
            auction.payment_deadline = timezone.now() + timedelta(minutes=20)
            print(f"✅ Manual close: Auction {auction.id} winner is {auction.winner.username} with bid ${winning_bid.amount}")

        auction.save()

        # Send notifications to winner
        print(f"🔍 DEBUG: Checking notifications - winning_bid: {winning_bid is not None}, winner: {auction.winner}")
        if winning_bid and auction.winner:
            print(f"🔍 DEBUG: Sending notifications to winner: {auction.winner.username}")
            try:
                # Send in-app notification first
                from notifications.services import NotificationService
                notification_service = NotificationService()

                print(f"🔍 DEBUG: Calling notification service...")
                notifications = notification_service.send_notification(
                    user=auction.winner,
                    notification_type='auction_won',
                    context={'auction': auction, 'bid': winning_bid}
                )
                print(f"✅ Winner notifications created: {len(notifications) if notifications else 0}")
                if notifications:
                    for notif in notifications:
                        print(f"   📱 {notif.channel}: {notif.status} - {notif.title}")

                # Send WhatsApp notification to winner
                from notifications.ultramsg_service import ultramsg_service

                message = f"""🎉 Congratulations! You won the auction!

📦 Auction: {auction.title}
💰 Winning Bid: ${auction.current_price}
⏰ Payment Deadline: 20 minutes

Please complete your payment within 20 minutes to secure your purchase.

Thank you for participating!"""

                print(f"🔍 DEBUG: UltraMsg configured: {ultramsg_service.is_configured()}")
                if ultramsg_service.is_configured():
                    print(f"🔍 DEBUG: Sending WhatsApp to: {auction.winner.phone_number}")
                    result = ultramsg_service.send_text_message(
                        phone_number=auction.winner.phone_number,
                        message=message
                    )
                    print(f"🔍 DEBUG: WhatsApp result: {result}")
                    if result.get('success'):
                        print(f"✅ WhatsApp notification sent to winner: {auction.winner.username}")
                    else:
                        print(f"❌ Failed to send WhatsApp: {result.get('error')}")
                else:
                    print(f"📱 WhatsApp not configured - would send to {auction.winner.username}")

                # Send notification to seller about the winner
                print(f"🔍 DEBUG: Sending seller notification...")
                seller_notifications = notification_service.send_notification(
                    user=auction.seller,
                    notification_type='auction_sold',
                    context={'auction': auction, 'bid': winning_bid, 'winner': auction.winner}
                )
                print(f"✅ Seller notifications created: {len(seller_notifications) if seller_notifications else 0}")

            except Exception as e:
                print(f"❌ Failed to send notifications to winner: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"🔍 DEBUG: No notifications sent - winning_bid: {winning_bid}, winner: {auction.winner}")

        # Send real-time update
        try:
            channel_layer = get_channel_layer()
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    f'auction_{auction.id}',
                    {
                        'type': 'auction_status_update',
                        'auction_data': {
                            'auction_id': auction.id,
                            'status': 'ended',
                            'end_time': auction.end_time.isoformat(),
                            'winner': auction.winner.username if auction.winner else None,
                            'final_price': str(auction.current_price),
                            'reason': 'target_price_reached'
                        }
                    }
                )
        except Exception as e:
            print(f"Failed to send auction close update: {e}")

        return Response({
            'message': 'Auction closed successfully',
            'auction': AuctionDetailSerializer(auction).data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def cancel_auction(request, auction_id):
    """Cancel a live auction without declaring a winner"""
    try:
        auction = get_object_or_404(Auction, id=auction_id)

        # Check if user is the seller
        if auction.seller != request.user:
            return Response(
                {'error': 'Only the seller can cancel the auction'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if auction is live
        if auction.status != 'live':
            return Response(
                {'error': 'Only live auctions can be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Cancel the auction
        auction.status = 'cancelled'
        auction.end_time = timezone.now()
        auction.save()

        # Send real-time update
        try:
            channel_layer = get_channel_layer()
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    f'auction_{auction.id}',
                    {
                        'type': 'auction_status_update',
                        'auction_data': {
                            'auction_id': auction.id,
                            'status': 'cancelled',
                            'end_time': auction.end_time.isoformat(),
                            'reason': 'cancelled_by_seller'
                        }
                    }
                )
        except Exception as e:
            print(f"Failed to send auction cancel update: {e}")

        return Response({
            'message': 'Auction cancelled successfully',
            'auction': AuctionDetailSerializer(auction).data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )




class AutoBidView(APIView):
    """Manage automatic bidding"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Set up automatic bidding",
        description="Configure automatic bidding for an auction",
        request=AutoBidSerializer
    )
    def post(self, request, auction_id):
        from django.db import transaction
        from decimal import Decimal
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync

        auction = get_object_or_404(Auction, id=auction_id)

        if request.user.user_type not in ['buyer', 'broker', 'admin']:
            return Response(
                {'error': 'Only buyers and brokers can set up automatic bidding'},
                status=status.HTTP_403_FORBIDDEN
            )

        if auction.seller == request.user:
            return Response(
                {'error': 'Cannot set up auto-bid on your own auction'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if auction.status != 'live':
            return Response(
                {'error': 'Can only set auto-bid on live auctions'},
                status=status.HTTP_400_BAD_REQUEST
            )

        max_amount = Decimal(str(request.data.get('max_amount', 0)))

        if max_amount <= auction.current_price:
            return Response(
                {'error': 'Max amount must be higher than current price'},
                status=status.HTTP_400_BAD_REQUEST
            )

        with transaction.atomic():
            # Check if auto-bid already exists
            auto_bid, created = AutoBid.objects.get_or_create(
                auction=auction,
                bidder=request.user,
                defaults={
                    'max_amount': max_amount,
                    'increment': auction.bid_increment,
                    'is_active': True
                }
            )

            if not created:
                # Update existing auto-bid
                auto_bid.max_amount = max_amount
                auto_bid.is_active = True
                auto_bid.save()

            # Place initial bid if current price is below max amount
            next_bid_amount = auction.current_price + auction.bid_increment

            if next_bid_amount <= auto_bid.max_amount:
                # Create initial auto-bid
                bid = Bid.objects.create(
                    auction=auction,
                    bidder=request.user,
                    amount=next_bid_amount,
                    bid_type='auto'
                )

                # Update auction
                auction.current_price = next_bid_amount
                auction.total_bids += 1
                auction.save()

                # Update auto-bid current amount
                auto_bid.current_bid_amount = next_bid_amount
                auto_bid.save()

                # Send real-time update
                channel_layer = get_channel_layer()
                async_to_sync(channel_layer.group_send)(
                    f'auction_{auction.id}',
                    {
                        'type': 'bid_update',
                        'bid_data': {
                            'auction_id': auction.id,
                            'bid_id': bid.id,
                            'amount': str(bid.amount),
                            'bidder': bid.bidder.username,
                            'timestamp': bid.timestamp.isoformat(),
                            'current_price': str(auction.current_price),
                            'total_bids': auction.total_bids,
                            'bid_type': 'auto'
                        }
                    }
                )

        serializer = AutoBidSerializer(auto_bid)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @extend_schema(
        summary="Get automatic bidding configuration",
        description="Get current automatic bidding configuration for an auction"
    )
    def get(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)

        try:
            auto_bid = AutoBid.objects.get(auction=auction, bidder=request.user)
            serializer = AutoBidSerializer(auto_bid)
            return Response(serializer.data)
        except AutoBid.DoesNotExist:
            return Response({'error': 'No automatic bidding configured'}, status=status.HTTP_404_NOT_FOUND)

    @extend_schema(
        summary="Delete automatic bidding",
        description="Remove automatic bidding configuration for an auction"
    )
    def delete(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)

        try:
            auto_bid = AutoBid.objects.get(auction=auction, bidder=request.user)
            auto_bid.delete()
            return Response({'message': 'Automatic bidding removed'})
        except AutoBid.DoesNotExist:
            return Response({'error': 'No automatic bidding configured'}, status=status.HTTP_404_NOT_FOUND)


class AuctionWatchlistView(generics.ListCreateAPIView):
    """Manage auction watchlist"""

    serializer_class = AuctionWatchlistSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return AuctionWatchlist.objects.filter(user=self.request.user)

    @extend_schema(
        summary="Get watchlist",
        description="Get user's auction watchlist"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Add to watchlist",
        description="Add an auction to watchlist"
    )
    def post(self, request, *args, **kwargs):
        auction_id = request.data.get('auction_id')
        if not auction_id:
            return Response({'error': 'auction_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        auction = get_object_or_404(Auction, id=auction_id)
        watchlist_item, created = AuctionWatchlist.objects.get_or_create(
            user=request.user,
            auction=auction
        )

        if created:
            serializer = self.get_serializer(watchlist_item)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response({'message': 'Auction already in watchlist'}, status=status.HTTP_200_OK)


class RemoveFromWatchlistView(APIView):
    """Remove auction from watchlist"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Remove from watchlist",
        description="Remove an auction from watchlist"
    )
    def delete(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)

        try:
            watchlist_item = AuctionWatchlist.objects.get(user=request.user, auction=auction)
            watchlist_item.delete()
            return Response({'message': 'Auction removed from watchlist'})
        except AuctionWatchlist.DoesNotExist:
            return Response({'error': 'Auction not in watchlist'}, status=status.HTTP_404_NOT_FOUND)


class MyAuctionsView(generics.ListAPIView):
    """Get user's auctions (for sellers)"""

    serializer_class = AuctionListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Auction.objects.filter(seller=self.request.user)

    @extend_schema(
        summary="Get my auctions",
        description="Get auctions created by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class MakeAuctionLiveView(APIView):
    """Make a draft auction live"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Make auction live",
        description="Change auction status from draft to live"
    )
    def post(self, request, auction_id):
        try:
            auction = get_object_or_404(
                Auction,
                id=auction_id,
                seller=request.user
            )

            if auction.status not in ['draft', 'scheduled']:
                return Response(
                    {'error': 'Only draft and scheduled auctions can be made live'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate auction has all required fields
            if not auction.start_time or not auction.end_time:
                return Response(
                    {'error': 'Auction must have start and end times'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if auction.start_time >= auction.end_time:
                return Response(
                    {'error': 'End time must be after start time'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update status to live
            auction.status = 'live'
            auction.save()

            return Response(
                {'message': 'Auction is now live', 'auction_id': auction.id},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AuctionUpdateView(generics.UpdateAPIView):
    """Update auction (only drafts can be updated)"""

    serializer_class = AuctionCreateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Auction.objects.filter(seller=self.request.user)

    def get_object(self):
        auction = super().get_object()
        if auction.status != 'draft':
            from rest_framework.exceptions import ValidationError
            raise ValidationError('Only draft auctions can be updated')
        return auction

    @extend_schema(
        summary="Update auction",
        description="Update a draft auction (only draft auctions can be updated)"
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

    @extend_schema(
        summary="Partially update auction",
        description="Partially update a draft auction"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class MyBidsView(generics.ListAPIView):
    """Get user's bids"""

    serializer_class = BidSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Bid.objects.filter(bidder=self.request.user).select_related('auction')

    @extend_schema(
        summary="Get my bids",
        description="Get bids placed by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class EarnedAuctionsView(generics.ListAPIView):
    """Get auctions won by the user"""

    serializer_class = AuctionDetailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Auction.objects.filter(winner=self.request.user).order_by('-end_time')

    @extend_schema(
        summary="Get earned auctions",
        description="Get auctions won by the authenticated user"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class AuctionBidsView(generics.ListAPIView):
    """Get bids for a specific auction"""

    serializer_class = BidSerializer
    permission_classes = [permissions.AllowAny]  # Allow anyone to view bids

    def get_queryset(self):
        auction_id = self.kwargs['auction_id']
        return Bid.objects.filter(auction_id=auction_id).select_related('bidder').order_by('-timestamp')

    @extend_schema(
        summary="Get auction bids",
        description="Get all bids for a specific auction"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class UpdateSellerLocationView(APIView):
    """Update seller's live location"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Update seller location",
        description="Update seller's current location for live tracking",
        request=UpdateSellerLocationSerializer
    )
    def post(self, request):
        if request.user.user_type not in ['seller', 'admin']:
            return Response(
                {'error': 'Only sellers can update location'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = UpdateSellerLocationSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data

            # Create new location record
            location = SellerLocation.objects.create(
                seller=request.user,
                auction_id=data.get('auction_id'),
                latitude=data['latitude'],
                longitude=data['longitude'],
                accuracy=data.get('accuracy'),
                altitude=data.get('altitude'),
                heading=data.get('heading'),
                speed=data.get('speed'),
                status=data.get('status', 'online'),
                address=data.get('address', ''),
                is_active=True
            )

            # Update auction location if auction_id provided
            if data.get('auction_id'):
                try:
                    auction = Auction.objects.get(id=data['auction_id'], seller=request.user)
                    auction.latitude = data['latitude']
                    auction.longitude = data['longitude']
                    auction.location_updated_at = timezone.now()
                    auction.is_location_live = True
                    auction.save(update_fields=['latitude', 'longitude', 'location_updated_at', 'is_location_live'])

                    # Send real-time location update via WebSocket
                    try:
                        channel_layer = get_channel_layer()
                        if channel_layer:
                            async_to_sync(channel_layer.group_send)(
                                f'auction_{auction.id}',
                                {
                                    'type': 'location_update',
                                    'location_data': {
                                        'auction_id': auction.id,
                                        'latitude': str(data['latitude']),
                                        'longitude': str(data['longitude']),
                                        'status': location.status,
                                        'address': location.address,
                                        'timestamp': location.timestamp.isoformat(),
                                        'seller_name': request.user.username
                                    }
                                }
                            )
                    except Exception as e:
                        print(f"Failed to send location update: {e}")

                except Auction.DoesNotExist:
                    pass

            return Response(SellerLocationSerializer(location).data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetSellerLocationView(APIView):
    """Get auction hunt location for bidders"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get auction hunt location",
        description="Get auction hunt place location (only for users who have bid)"
    )
    def get(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)

        # Check if user has bid on this auction (only bidders can see location)
        user_has_bid = auction.bids.filter(bidder=request.user).exists()
        if not user_has_bid:
            return Response(
                {'error': 'You must place a bid to view hunt location'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if auction has location coordinates
        if auction.latitude is not None and auction.longitude is not None:
            return Response({
                'latitude': str(auction.latitude),
                'longitude': str(auction.longitude),
                'location_updated_at': auction.location_updated_at.isoformat() if auction.location_updated_at else None,
                'is_location_live': auction.is_location_live,
                'seller_name': auction.seller.username,
                'status': 'hunt_location',
                'location_type': 'auction_hunt_place'
            })

        return Response({'error': 'Hunt location not available'}, status=status.HTTP_404_NOT_FOUND)


class ToggleLocationSharingView(APIView):
    """Toggle location sharing for seller"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Toggle location sharing",
        description="Enable or disable live location sharing for seller"
    )
    def post(self, request, auction_id):
        if request.user.user_type not in ['seller', 'admin']:
            return Response(
                {'error': 'Only sellers can toggle location sharing'},
                status=status.HTTP_403_FORBIDDEN
            )

        auction = get_object_or_404(Auction, id=auction_id, seller=request.user)

        # Toggle location sharing
        auction.is_location_live = not auction.is_location_live
        if not auction.is_location_live:
            # Clear location data when disabled
            auction.latitude = None
            auction.longitude = None
            auction.location_updated_at = None

        auction.save(update_fields=['is_location_live', 'latitude', 'longitude', 'location_updated_at'])

        # Deactivate all location records for this auction if disabled
        if not auction.is_location_live:
            SellerLocation.objects.filter(
                seller=request.user,
                auction=auction
            ).update(is_active=False)

        return Response({
            'is_location_live': auction.is_location_live,
            'message': f"Location sharing {'enabled' if auction.is_location_live else 'disabled'}"
        })


class AuctionLocationView(APIView):
    """Get auction with location data"""

    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Get auction location data",
        description="Get auction details with location information"
    )
    def get(self, request, auction_id):
        auction = get_object_or_404(Auction, id=auction_id)
        serializer = AuctionLocationSerializer(auction)
        return Response(serializer.data)
